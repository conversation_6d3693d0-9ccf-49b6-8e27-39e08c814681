#!/bin/sh
npm run pre-push

echo "
                       ______________
                       \\\\_____()_____/         /  )
                       '============\`        /  /
                        #---\\\\  /---#        /  /
                       (# @\\\\| |/@  #)      /  /
                        \\\\   (_)   /       /  /
                        |\\\\ '---\` /|      /  /
                _______/ \\\\\\\\_____// \\\\____/ o_|
               /       \\\\  /     \\\\  /   / o_|
              / |           o|        / o_| \\\\
             /  |  _____     |       / /   \\\\ \\\\
            /   |  |===|    o|      / /\\    \\\\ \\\\
           |    |   \\\\@/      |     / /  \\\\    \\\\ \\\\
           |    |___________o|__/----)   \\\\    \\\\/
           |    '\\              || --)    \\\\     |
           |___________________||  --)     \\\\    /
                |           o|   ''''   |   \\\\__/
                |            |          |
"
echo "\033[0;31m If you skip the manual/visual test of the build before creating a PR, that's a paddlin! \033[0m"
echo "\n\n"
# Roar BFF RM API

## Overview

Welcome to the Roar BFF RM API! This project serves as a Backend for Frontend (BFF) layer, acting as an API composition pattern service that aggregates data from multiple microservices such as RM Cal 2.1 API, Platform, Projects, Timesheets, and ICA. The API is built using GraphQL with Apollo Server and follows the Clean Architecture principles. The main purpose of this API is to provide a unified interface to access and manage data across different services.

## Project Structure

The project is organized into several layers according to the Clean Architecture principles. Each layer has a specific responsibility:

1. Domain Layer: Contains the core business logic and domain models.
2. Application Layer: Contains the use cases and service classes that orchestrate the business logic.
3. Infrastructure Layer: Contains implementations for data access (e.g., repositories) and external services.
4. API Layer: Contains GraphQL resolvers, type definitions, and other API-related configurations.

## Folder Structure

src/<br/>
├── Application/<br/>
│ ├── Common/<br/>
│ ├── ResourceSlice/<br/>
│ ├── TaskAssignmentSlice/<br/>
│ ├── UsersGroupsSlice/<br/>
│ ├── UsersSlice/<br/>
├── Domain/<br/>
│ ├── Common/<br/>
│ ├── ResourceSlice/<br/>
│ ├── TaskAssignmentSlice/<br/>
│ ├── UserGroupsSlice/<br/>
│ ├── UsersSlice/<br/>
├── GraphQL/<br/>
│ ├── ResourceSlice/<br/>
│ ├── TaskAssignmentSlice/<br/>
│ ├── UsersGroupsSlice/<br/>
│ ├── UsersSlice/<br/>
├── Infrastructure/<br/>
│ ├── ResourceSlice/<br/>
│ ├── TaskAssignmentSlice/<br/>
│ ├── UserGroupsSlice/<br/>
│ ├── UsersSlice/<br/>
├── Config/<br/>
├── index.ts<br/>
└── ...<br/>

## Libraries Used

- **Apollo Server**: A fully-featured GraphQL Server with out-of-the-box support for handling queries, mutations, and subscriptions.
- **GraphQL**: A query language for your API, providing a complete and understandable description of the data in your API.
- **TypeScript**: A strongly typed programming language that builds on JavaScript, giving you better tooling at any scale.
- **tsyringe**: A lightweight dependency injection container for TypeScript/JavaScript, helping with cleaner and more maintainable code.
- **lodash**: A modern JavaScript utility library delivering modularity, performance, and extras.
- **reflect-metadata**: A polyfill that enables metadata reflection API for TypeScript decorators.
- **dotenv**: A module that loads environment variables from a .env file into process.env.
- **nodemon**: A tool that helps develop node.js based applications by automatically restarting the node application when file changes are detected.
- **ESLint**: A tool for identifying and reporting on patterns found in ECMAScript/JavaScript code.
- **Prettier**: An opinionated code formatter.

## Clean Architecture

Clean Architecture promotes the separation of concerns by dividing the code into layers, each with a specific responsibility. This architecture helps to create a more maintainable, testable, and scalable application.

![Clean Architecture Dependencies](https://developers.redhat.com/sites/default/files/clean_architecture_dependencies.jpg 'Clean Architecture Dependencies')

### Layers

2. **Domain Layer**: This layer contains the business logic and domain models (e.g., Resource, Assignment, User). These models represent the core entities in the system and should be independent of any external framework or database.

3. **Application Layer**: This layer defines the application-specific logic, such as services that perform operations on the domain models. It interacts with the domain layer and coordinates data flow between the domain and infrastructure layers.

4. **Infrastructure Layer**: This layer contains the implementation details for interacting with external systems (e.g., databases, external APIs). The repository classes in this layer implement interfaces defined in the application layer.

5. **API Layer (GraphQL)**: This layer handles the incoming HTTP requests and outgoing responses. It defines the GraphQL schemas, resolvers, and context used to serve the API requests.

### Applying Clean Architecture in the Project

In this project, the ResourceSlice serves as a vertical slice that encapsulates all related functionalities for resources. Here's how Clean Architecture is applied:

- **Domain Layer**: Defines the Resource model and any related domain logic.
- **Application Layer**: Contains the ResourcesService, which handles use cases like fetching or updating resources. It interacts with the IResourceRepository interface to decouple the service from the actual implementation.
- **Infrastructure Layer**: Implements the IResourceRepository in ResourcesRepository, which handles data fetching from external services or databases.
- **API Layer**: Uses GraphQL to expose the ResourcesService through resolvers and type definitions.

## Vertical Slicing

Vertical Slicing is an approach where each feature or functionality (slice) is implemented end-to-end, covering all necessary layers (Domain, Application, Infrastructure, API). This approach improves code organization and makes the system easier to extend or modify.

### Example: Resource Slice

- **Domain Layer:**
  - `Domain/ResourceSlice/resource.ts` - Defines the Resource model.
- **Application Layer:**
  - `Application/ResourceSlice/resourceService.ts` - Implements the business logic related to resources.
  - `Application/ResourceSlice/iResourceRepository.ts` - Defines the interface for the repository.
- **Infrastructure Layer:**
  - `Infrastructure/ResourceSlice/resourceRepository.ts` - Implements the data access logic for resources.
- **API Layer:**
  - `GraphQL/ResourceSlice/ResourceResolver.ts` - Defines the GraphQL resolvers for resources using an interface-driven approach. They should implement IResolver interface and registered in the `GraphQL/index.ts` in the `resolversArray`.
  - `GraphQL/ResourceSlice/ResourceTypeDefs.ts` - Defines the GraphQL schema for resources. They should be registered in the `GraphQL/index.ts` in the `typeDefs`.
  - `GraphQL/ResourceSlice/injectorConfig.ts` - Configures the dependency injection for the complete resource slice.

### Guidelines for Maintaining Slices

- Each slice should contain all relevant files and logic related to a specific feature (e.g., Resource, Assignment).
- Follow the same structure for every new slice: Domain, Application, Infrastructure, API.
- Ensure that each slice has its own GraphQL resolvers, services, and repositories.
- Use consistent naming conventions and directory structures to maintain clarity and organization.

## Coding Conventions

This project follows a strict set of coding conventions to maintain code quality and consistency:

- **TypeScript**: All code should be written in TypeScript for type safety and better tooling.
- **ESLint and Prettier**: Code linting and formatting are enforced using ESLint and Prettier. The project follows the Airbnb style guide with custom rules.
- **Dependency Injection**: Use tsyringe for dependency injection, ensuring that services and repositories are loosely coupled and easily testable.
- **Environment Variables**: Configuration values should be stored in environment variables using dotenv.
- **Error Handling**: All errors should be handled gracefully with appropriate logging using winston.
- **Naming Conventions**: Use consistent and descriptive naming for files, classes, methods, and variables to enhance readability.
- **Documentation**: Comment and document code where necessary to explain complex logic and decisions.

## Getting Started

### Prerequisites

- **Node.js**: Ensure that you have Node.js installed.
- **npm**: Ensure that you have npm installed.

### InstallationInstallation

1. Clone the repository:

```sh
    git clone <repository-url>
    cd roar-bff-rm
    cp .env.local.example .env.local
```

2. Install dependencies:

```sh
    npm install
```

3. Set up environment variables: Create a .env.local file in the root directory with the required environment variables, such as PLATFORM_URL, API_KEY, etc.

### Running the Application

- Development:

```sh
    npm run dev
```

- Production:

```sh
    npm run start
```

### Linting and Formatting

- Linting:

```sh
    npm run lint
```

- Formatting:

```sh
        npm run format
```

- Testing
  While testing is not currently implemented in the project, it is recommended to follow the TDD approach and use testing libraries like Jest for unit and integration tests.

## Running and Debugging

### Running the Application

To run the application in development mode with automatic restarts on code changes,

```sh
    npm run dev
```

To build and run the application in production mode, use:

```sh
    npm run start
```

### DebuggingDebugging

To debug the application, follow these steps:
**Using Visual Studio Code (VS Code)**

1. Install Dependencies: Ensure that all dependencies are installed by running:

```sh
    npm install
```

2. Build the Project: Compile the TypeScript code:

```sh
    npm run compile
```

## Conclusion

The Roar BFF RM API provides a clean, scalable, and maintainable architecture that is designed to handle complex integrations with multiple external services. By adhering to Clean Architecture principles and following the coding conventions outlined in this README, contributors can ensure that the project remains robust and easy to extend in the future.

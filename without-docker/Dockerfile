# Use an official Node.js image as the base image
FROM node:22.3.0-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy the package.json and package-lock.json files
COPY package*.json ./

# Clean npm cache and install project dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Ensure there are no root-owned files in the app directory
RUN chown -R node:node /app

# Switch to non-root user
USER node

# Expose the port on which the application will run
EXPOSE 4000

# Command to run the application
CMD ["npm", "start"]
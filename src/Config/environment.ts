import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

export const config = {
  platformApiKey: process.env.PLATFORM_API_KEY,
  platformUrl: process.env.PLATFORM_URL,
  projectUrl: process.env.PROJECTS_URL,
  timesheetsUrl: process.env.TIMESHEETS_URL,
  rmApiUrl: process.env.RM_URL,
  cacheExpirationTime: process.env.CACHE_EXPIRATION_TIME, // Global expiration time fallback for cache, if no value is provided in the decorator
  projectsCacheExpirationTime: process.env.PROJECTS_CACHE_EXPIRATION_TIME,
  websiteName: process.env.WEB_APP_ENV_NAME,
  azureInstrumentationKey: process.env.APPINSIGHTS_INSTRUMENTATION_KEY,
  usersGroupsCacheExpirationTime: process.env.USER_GROUPS_CACHE_EXPIRATION_TIME,
  resourcesCacheExpirationTime: process.env.RESOURCES_CACHE_EXPIRATION_TIME,
  projectsApiScopes: process.env.AZURE_AD_SCOPES_PROJECT,
  platformApiScopes: process.env.AZURE_AD_SCOPES_PLATFORM,
  timesheetsApiScopes: process.env.AZURE_AD_SCOPES_TIMESHEETS,
  azureAdTenantId: process.env.AZURE_AD_TENANT_ID,
  azureAdClientId: process.env.AZURE_AD_CLIENT_ID_API,
  azureAdClientSecret: process.env.AZURE_AD_CLIENT_SECRET_API
}

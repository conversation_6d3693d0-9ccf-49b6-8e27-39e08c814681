import { GraphQLError } from 'graphql/error'
import { container } from 'tsyringe'
import { UserSessionContext } from './Application/AuthSlice/UserSessionContext'
import { PublicQueriesConfig } from './GraphQL/Common/PublicQueries'
import { ErrorFactory } from './GraphQL/Common/ErrorCodes'
import { AuthConfig } from './GraphQL/Common/AuthConfig'

/* eslint-disable @typescript-eslint/no-explicit-any */
export const context = async ({ req }: any) => {
  const authToken = req.headers.authorization || ''

  const isPublicQuery = req.body?.query && PublicQueriesConfig.isPublicQuery(req.body.query)

  if (!authToken && !isPublicQuery) {
    const error = ErrorFactory.createAuthorizationError()
    throw new GraphQLError(error.message, {
      extensions: { code: error.code }
    })
  }

  const requestContainer = container.createChildContainer()

  if (authToken) {
    const profile = req.headers.profile
    const userData = AuthConfig.parseJwtToken(authToken, profile)
    const user = requestContainer.resolve(UserSessionContext)

    user.setUserSession({
      ...userData,
      token: authToken
    })
  }

  return AuthConfig.createContextConfig(requestContainer)
}

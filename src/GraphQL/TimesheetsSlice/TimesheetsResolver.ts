import { inject, injectable } from 'tsyringe'
import { IResolver } from '../Common/IResolver'
import TimesheetsService from '../../Application/TimesheetsSlice/TimesheetsService'
import { TimeOffRequest, TimesheetsRequest } from '../../Application/TimesheetsSlice/TimesheetsRequests'
import { TimeOffDetails, Timesheets } from '../../Domain/TimesheetsSlice/Timesheets'

@injectable()
export class TimesheetsResolver implements IResolver {
  constructor(
    @inject('TimesheetsService')
    private TimesheetsService: TimesheetsService
  ) {}

  getQueries() {
    return {
      getTimesheetsForResource: (_, { params }) => this.getTimesheetsForResource(params),
      getTimeOffForResource: (_, { params }) => this.getTimeOffForResource(params)
    }
  }

  getMutations() {
    return {}
  }

  private async getTimesheetsForResource(params: TimesheetsRequest): Promise<Timesheets[]> {
    return await this.TimesheetsService.getTimesheetsForResource(params)
  }

  private async getTimeOffForResource(params: TimeOffRequest): Promise<TimeOffDetails[]> {
    return await this.TimesheetsService.getTimeOffForResource(params)
  }
}

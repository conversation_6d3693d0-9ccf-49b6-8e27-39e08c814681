// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { ITimesheetsRepository } from '../../Application/TimesheetsSlice/TimesheetsRepository'
import TimesheetsRepository from '../../Infrastructure/TimesheetsSlice/TimesheetsRepository'

// Service imports
import { TimesheetsResolver } from './TimesheetsResolver'
import TimesheetsService from '../../Application/TimesheetsSlice/TimesheetsService'

container.register<TimesheetsResolver>('TimesheetsResolver', {
  useClass: TimesheetsResolver
})

container.register<ITimesheetsRepository>('ITimesheetsRepository', {
  useClass: TimesheetsRepository
})

container.register<TimesheetsService>('TimesheetsService', {
  useClass: TimesheetsService
})

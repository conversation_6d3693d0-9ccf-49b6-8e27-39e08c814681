/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
      type Timesheets {
        project: String
        task: String
        user: String
        timesheetDate: String
        timesheetStatus: String
        hours: Float
      }

      input TimesheetsRequest {
        jobId: String
        taskId: String
        employeeCode: String
        assignmentStartDate: String
        assignmentEndDate: String
      }

      input TimeOffRequest{
        employeeCode: String
        weekStartDate: String
        weekEndDate: String
      }

      type TimeOffDetails {
        user: String
        absenceDate: String
        timeOffHours: String
        absenceStatus: String
      }
    `,

  Query: `#graphql
      getTimesheetsForResource(params:TimesheetsRequest): [Timesheets]
      getTimeOffForResource(params:TimeOffRequest): [TimeOffDetails]  
    `
}

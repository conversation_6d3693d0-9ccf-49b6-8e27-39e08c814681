// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IResourceRepository } from '../../Application/ResourceSlice/IResourceRepository'
import ResourceRepository from '../../Infrastructure/ResourceSlice/ResourceRepository'
import { IResourcePlatformRepository } from '../../Application/ResourceSlice/IResourcePlatformRepository'
import ResourcePlatformRepository from '../../Infrastructure/ResourceSlice/ResourcePlatformRepository'

// Service imports
import ResourceService from '../../Application/ResourceSlice/ResourceService'
import { ResourceResolver } from './ResourceResolver'

container.register<ResourceResolver>('ResourceResolver', {
  useClass: ResourceResolver
})

container.register<IResourceRepository>('IResourceRepository', {
  useClass: ResourceRepository
})

container.register<IResourcePlatformRepository>('IResourcePlatformRepository', {
  useClass: ResourcePlatformRepository
})

container.register<ResourceService>('ResourceService', {
  useClass: ResourceService
})

import { inject, injectable } from 'tsyringe'
import ResourceService from '../../Application/ResourceSlice/ResourceService'
import { ResourceRequest } from '../../Application/ResourceSlice/ResourceRequest'
import { IResolver } from '../Common/IResolver'
import { ResourceDto } from '../../Application/ResourceSlice/ResourceDto'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { ResourceOrgStructure } from '../../Domain/ResourceSlice/ResourceOrgStructure'
import { Agency } from '../../Domain/ResourceSlice/Agency'
import { CostCenter } from '../../Domain/ResourceSlice/CostCenter'
import { OrgRequest } from '../../Application/ResourceSlice/OrgRequest'
import { HolidayAndTimeOffRequest } from '../../Application/ResourceSlice/HolidayAndTimeOffRequest'
import { HolidayAndTimeOffDto } from '../../Application/ResourceSlice/HolidayAndTimeOffDto'
import { ResourceByFiltersRequest } from '../../Application/ResourceSlice/ResourceByFiltersRequest'
import { GetOrgStructureByResourceAssignmentsRequest } from '../../Domain/ResourceSlice/GetOrgStructureByResourceAssignmentsRequest'
import { GetOrgStructureByAssignmentsResponse } from '../../Domain/PlaceholderSlice/GetOrgStructureByAssignmentsResponse'

@injectable()
export class ResourceResolver implements IResolver {
  constructor(
    @inject('ResourceService')
    private resourcesService: ResourceService
  ) {}

  getQueries() {
    return {
      resources: (_, { params }) => this.resources(params),
      getResourceById: (_, { resourceId }) => this.getResourceById(resourceId),
      getResourceOrgStructure: (_, { resourceId }) => this.getResourceOrgStructure(resourceId),
      searchAgencyOrgStructure: (_, { searchTerm }) => this.searchAgencyOrgStructure(searchTerm),
      getLocationByAgencyOrgStructure: (_, { agencyCode }) => this.getLocationByAgencyOrgStructure(agencyCode),
      getLocationsByAgenciesOrgStructure: (_, { agencyCodes }) => this.getLocationsByAgenciesOrgStructure(agencyCodes),
      getCostCenterByLocationOrgStructure: (_, { agencyCode, city }) =>
        this.getCostCenterByLocationOrgStructure(agencyCode, city),
      getCostCentersByLocationsOrgStructure: (_, { agencyCodes, cities }) =>
        this.getCostCentersByLocationsOrgStructure(agencyCodes, cities),
      getHolidaysAndTimeOffDetailsByResourceId: (_, { params }) =>
        this.getHolidaysAndTimeOffDetailsByResourceId(params),
      getOrgStructureByAgencyCodeCostCenterCode: (_, { params }) =>
        this.getOrgStructureByAgencyCodeCostCenterCode(params),
      getOrgStructureByAgencyCodesCostCenterCodes: (_, { params }) =>
        this.getOrgStructureByAgencyCodesCostCenterCodes(params),
      resourcesByFilters: (_, { params }) => this.resourcesByFilters(params),
      getOrgStructureByResourceAssignments: (_, { params }) => this.getOrgStructureByResourceAssignments(params)
    }
  }

  getMutations() {
    return {}
  }

  private async resources(params: ResourceRequest): Promise<PagingResult<ResourceDto>> {
    return await this.resourcesService.getResources(params)
  }

  private async getResourceById(resourceId: string): Promise<ResourceDto> {
    return await this.resourcesService.getResourceById(resourceId)
  }

  private async getResourceOrgStructure(resourceId: string): Promise<ResourceOrgStructure> {
    return await this.resourcesService.getResourceOrgStructure(resourceId)
  }

  private async searchAgencyOrgStructure(searchTerm: string): Promise<Agency[]> {
    return await this.resourcesService.searchAgencyOrgStructure(searchTerm)
  }

  private async getLocationByAgencyOrgStructure(agencyCode: string): Promise<string[]> {
    return await this.resourcesService.getLocationByAgencyOrgStructure(agencyCode)
  }

  private async getLocationsByAgenciesOrgStructure(agencyCodes: string[]): Promise<string[]> {
    return await this.resourcesService.getLocationsByAgenciesOrgStructure(agencyCodes)
  }

  private async getCostCenterByLocationOrgStructure(agencyCode: string, city: string): Promise<CostCenter[]> {
    return await this.resourcesService.getCostCenterByLocationOrgStructure(agencyCode, city)
  }

  private async getCostCentersByLocationsOrgStructure(agencyCodes: string[], cities: string[]): Promise<CostCenter[]> {
    return await this.resourcesService.getCostCentersByLocationsOrgStructure(agencyCodes, cities)
  }

  private async getOrgStructureByAgencyCodeCostCenterCode(params: OrgRequest): Promise<ResourceOrgStructure> {
    return await this.resourcesService.getOrgStructureByAgencyCodeCostCenterCode(params)
  }

  private async getOrgStructureByAgencyCodesCostCenterCodes(params: OrgRequest[]): Promise<ResourceOrgStructure[]> {
    return await this.resourcesService.getOrgStructureByAgencyCodesCostCenterCodes(params)
  }

  private async getHolidaysAndTimeOffDetailsByResourceId(
    params: HolidayAndTimeOffRequest
  ): Promise<HolidayAndTimeOffDto> {
    return await this.resourcesService.getHolidaysAndTimeOffDetailsByResourceId(params)
  }

  private async resourcesByFilters(params: ResourceByFiltersRequest): Promise<PagingResult<ResourceDto>> {
    return await this.resourcesService.getResourcesByFilters(params)
  }

  private async getOrgStructureByResourceAssignments(
    params: GetOrgStructureByResourceAssignmentsRequest
  ): Promise<GetOrgStructureByAssignmentsResponse> {
    return await this.resourcesService.getOrgStructureByResourceAssignments(params)
  }
}

/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
    type Resource {
      id: ID
      integrationId: String
      name: String
      location: String
      position: String
      jobTitle: String
      profitCenter: String
      altairNumber: String
      totalCapacity: Float
      minimumAgencyHoursPerDay: Float
      workCode: String
      agencyName: String
      agencyCode: String
      timeOffDetails: [TimeOffDetails]
      holidays: [Holiday]
      requiresAssignApproval: Boolean
    }

    type ResourcePaginated {
      items: [Resource]
      pageNumber: Int
      totalPages: Int
      totalCount: Int
      hasPreviousPage: Boolean
      hasNextPage: Boolean
    }

    type ResourceOrgStructure{
      agencyName: String
      locationName: String
      costCenterName: String
      agencyCode: String
      costCenterCode: String
    }

    input SortInput {
      field: String
      order: String
    }

    input ResourcesParams {
      userId: String!
      startDate: String!
      endDate: String!
      pageNumber:Int!
      pageSize: Int!
      sort: SortInput
      searchName: String
    }

     type Agency{
     agencyCode: String
     agencyName: String
    }

    type CostCenter{
      costCenterName: String
      costCenterCode: String
    }
    
    input OrgParams{
      agencyCode: String
      costCenterCode: String
    }
      
    type Holiday{
      holidayDate: String
      holidayName: String
      holidayCode: String
    }

    input HolidayAndTimeOffParams {
      resourceId: String!
      startDate: String!
      endDate: String!
    }

    type HolidaysAndTimeOffDetails {
      timeOffDetails: [TimeOffDetails]
      holidays: [Holiday]
    }

    input ResourcesByFiltersParams {
      userId: String  
      startDate: String
      endDate: String
      projects: [String]
      resources: [String]
      placeholders: [String]
      brands: [String]
      taskStates: [String]
      tasks: [String]
      usersGroups: [String]
      pageNumber:Int!
      pageSize: Int!
      sort: SortInput
    }

    input GetOrgStructureByResourceAssignmentsParams {
        userLoggedInExternalId: String!
      }

      type Agency{
        agencyCode: String
        agencyName: String
      }

      type CostCenter{
        costCenterName: String
        costCenterCode: String
      }

      type GetOrgStructureByAssignmentsResponse {
        agencies: [Agency]
        costCenters: [CostCenter]
        locations: [String]
      }
  `,

  Query: `#graphql
    resources(params: ResourcesParams): ResourcePaginated,
    getResourceById(resourceId: ID): Resource,
    getResourceOrgStructure(resourceId: ID): ResourceOrgStructure
    searchAgencyOrgStructure(searchTerm: String): [Agency]
    getLocationByAgencyOrgStructure(agencyCode: String!): [String]
    getLocationsByAgenciesOrgStructure(agencyCodes: [String]): [String]
    getOrgStructureByAgencyCodeCostCenterCode(params: OrgParams): ResourceOrgStructure
    getOrgStructureByAgencyCodesCostCenterCodes(params: [OrgParams]): [ResourceOrgStructure]
    getCostCenterByLocationOrgStructure(agencyCode: String!, city: String): [CostCenter]
    getCostCentersByLocationsOrgStructure(agencyCodes: [String], cities: [String]): [CostCenter]
    getHolidaysAndTimeOffDetailsByResourceId(params: HolidayAndTimeOffParams): HolidaysAndTimeOffDetails
    resourcesByFilters(params: ResourcesByFiltersParams): ResourcePaginated
    getOrgStructureByResourceAssignments(params: GetOrgStructureByResourceAssignmentsParams): GetOrgStructureByAssignmentsResponse
  `
}

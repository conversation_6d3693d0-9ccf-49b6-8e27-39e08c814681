// Injector imports
import 'reflect-metadata'
import { container } from 'tsyringe'

// Repository imports
import { IUserRepository } from '../../Application/UsersSlice/IUserRepository'
import UserRepository from '../../Infrastructure/UsersSlice/UserRepository'

// Service imports
import UserService from '../../Application/UsersSlice/UserService'
import { UserResolver } from './UserResolver'

container.register<UserResolver>('UserResolver', {
  useClass: UserResolver
})

container.register<IUserRepository>('IUserRepository', {
  useClass: UserRepository
})

container.register<UserService>('UserService', {
  useClass: UserService
})

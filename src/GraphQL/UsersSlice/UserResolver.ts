// src/GraphQL/UsersSlice/UserResolver.ts
import { inject, injectable } from 'tsyringe'
import UserService from '../../Application/UsersSlice/UserService'
import { User } from '../../Domain/UsersSlice/User'
import { IResolver } from '../Common/IResolver'

@injectable()
export class UserResolver implements IResolver {
  constructor(
    @inject('UserService')
    private usersService: UserService
  ) {}

  getQueries() {
    return {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      user: (_, { llid }) => this.user(llid)
    }
  }

  getMutations() {
    return {}
  }

  private async user(llid: string): Promise<User> {
    return await this.usersService.getUserByLlid(llid)
  }
}

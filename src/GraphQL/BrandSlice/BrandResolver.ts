import { inject, injectable } from 'tsyringe'
import BrandService from '../../Application/BrandSlice/BrandService'
import { Brand } from '../../Domain/BrandSlice/Brand'
import { IResolver } from '../Common/IResolver'

@injectable()
export class BrandResolver implements IResolver {
  constructor(
    @inject('BrandService')
    private brandService: BrandService
  ) {}

  getQueries() {
    return {
      getBrands: (_, { params }) => this.getBrands(params)
    }
  }

  getMutations() {
    return {}
  }

  private async getBrands(params: string[]): Promise<Brand[]> {
    return await this.brandService.getBrands(params)
  }
}

// Injector imports
import 'reflect-metadata'
import { container } from 'tsyringe'

// Repository imports
import { IBrandRepository } from '../../Application/BrandSlice/BrandRepository'
import BrandRepository from '../../Infrastructure/BrandSlice/BrandRepository'

// Service imports
import BrandService from '../../Application/BrandSlice/BrandService'
import { BrandResolver } from './BrandResolver'

container.register<BrandResolver>('BrandResolver', {
  useClass: BrandResolver
})

container.register<IBrandRepository>('IBrandRepository', {
  useClass: BrandRepository
})

container.register<BrandService>('BrandService', {
  useClass: BrandService
})

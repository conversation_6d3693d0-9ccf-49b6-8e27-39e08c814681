import { inject, injectable } from 'tsyringe'
import { IResolver } from '../Common/IResolver'
import { ICacheService } from '../../Application/Common/ICacheService'

@injectable()
export class AuthResolver implements IResolver {
  constructor(
    @inject('ICacheService')
    private cacheService: ICacheService
  ) {}

  getQueries() {
    return {}
  }

  getMutations() {
    return {
      login: (_, { employeeCode }) => this.login(employeeCode),
      logout: (_, { employeeCode }) => this.logout(employeeCode)
    }
  }

  private async login(employeeCode: string): Promise<boolean> {
    await this.cacheService.removeCachedValuesFromKeyVault(employeeCode)
    return true
  }

  private async logout(employeeCode: string): Promise<boolean> {
    await this.cacheService.removeCachedValuesFromKeyVault(employeeCode)
    return true
  }
}

import { inject, injectable } from 'tsyringe'
import { Workfront } from '../../Domain/WorkfrontSlice/Workfront'
import { IResolver } from '../Common/IResolver'
import WorkfrontService from '../../Application/WorkfrontSlice/WorkfrontService'

@injectable()
export class WorkfrontResolver implements IResolver {
  constructor(
    @inject('WorkfrontService')
    private workfrontService: WorkfrontService
  ) {}

  getQueries() {
    return {
      getWorkfrontUser: (_, { hostname, imsToken, userID }: { hostname: string; imsToken: string; userID: string }) =>
        this.getWorkfrontUser(hostname, imsToken, userID)
    }
  }

  getMutations() {
    return {}
  }

  private async getWorkfrontUser(hostname: string, imsToken: string, userID: string): Promise<Workfront> {
    return await this.workfrontService.getWorkfrontUser({ hostname, imsToken, userID })
  }
}

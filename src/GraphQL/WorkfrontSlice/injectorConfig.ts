// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IWorkfrontRepository } from '../../Application/WorkfrontSlice/WorkfrontRepository'
import WorkfrontRepository from '../../Infrastructure/WorkfrontSlice/WorkfrontRepository'

// Service imports
import { WorkfrontResolver } from './WorkfrontResolver'
import WorkfrontService from '../../Application/WorkfrontSlice/WorkfrontService'

container.register<WorkfrontResolver>('WorkfrontResolver', {
  useClass: WorkfrontResolver
})

container.register<IWorkfrontRepository>('IWorkfrontRepository', {
  useClass: WorkfrontRepository
})

container.register<WorkfrontService>('WorkfrontService', {
  useClass: WorkfrontService
})

// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IPlaceholderRepository } from '../../Application/PlaceholderSlice/IPlaceholderRepository'
import PlaceholderRepository from '../../Infrastructure/PlaceholderSlice/PlaceholderRepository'

// Service imports
import PlaceholderService from '../../Application/PlaceholderSlice/PlaceholderService'
import { PlaceholderResolver } from './PlaceholderResolver'
import ResourcePlatformRepository from '../../Infrastructure/ResourceSlice/ResourcePlatformRepository'

container.register<PlaceholderResolver>('PlaceholderResolver', {
  useClass: PlaceholderResolver
})

container.register<IPlaceholderRepository>('IPlaceholderRepository', {
  useClass: PlaceholderRepository
})

container.register<PlaceholderService>('PlaceholderService', {
  useClass: PlaceholderService
})

container.register<ResourcePlatformRepository>('ResourcePlatformRepository', {
  useClass: ResourcePlatformRepository
})

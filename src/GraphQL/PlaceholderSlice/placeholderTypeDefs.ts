/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
      type Placeholder {
        id: ID
        name: String
        minimumAgencyHoursPerDay: Float        
      }
  
      type PlaceholderPaginated {
        items: [Placeholder]
        pageNumber: Int
        totalPages: Int
        totalCount: Int
        hasPreviousPage: Boolean
        hasNextPage: Boolean
      }
  
      input SortInput {
        field: String
        order: String
      }
  
      input PlaceholdersParams {
        userId: String!
        pageNumber:Int!
        pageSize: Int!
        sort: SortInput
        isExpandedPlaceholderApplied: Boolean
      }

      input PlaceholderFiltersParams {
        userId: String!
        startDate: String!
        endDate: String!
        projects: [String]
        resources: [String]
        placeholders: [String]
        brands: [String]
        taskStates: [String]
        tasks: [String]
        usersGroups: [String]        
        pageNumber:Int!
        pageSize: Int!
        sort: SortInput
        isExpandedPlaceholderApplied: Boolean
      }

      input PlaceholderFilterValueParams {
        userLoggedInExternalId: String!
      }

      type PlaceholderFilterValues {
        userId: String
        businessUnit: [FilterValueList]
        costCenter: [FilterValueList]
        location: [FilterValueList]
        jobrole: [FilterValueList]
      }

      type FilterValueList {
        id: String
        label: String
      }
        
      type PlaceholderByWorkCode {
        workCode: String
        workCodeName: String      
      }

      input CreateEditPlaceholderFilterValuesRequest {
          userLoggedInExternalId: String
          businessUnit: [String]
          costCenter: [String]
          location: [String]
          jobrole: [String]
      }

      input GetOrgStructureByPlaceholderAssignmentsParams {
        userLoggedInExternalId: String!
      }

      type Agency{
        agencyCode: String
        agencyName: String
      }

      type CostCenter{
        costCenterName: String
        costCenterCode: String
      }

      type GetOrgStructureByAssignmentsResponse {
        agencies: [Agency]
        costCenters: [CostCenter]
        locations: [String]
      }
    `,

  Query: `#graphql
      placeholders(params: PlaceholdersParams): PlaceholderPaginated
      placeholderFilters(params: PlaceholderFiltersParams): PlaceholderPaginated
      getPlaceholderFilterValuesByUserId(params: PlaceholderFilterValueParams): PlaceholderFilterValues
      getPlaceholderByWorkCode(workCode: String): [PlaceholderByWorkCode]
      getOrgStructureByPlaceholderAssignments(params: GetOrgStructureByPlaceholderAssignmentsParams): GetOrgStructureByAssignmentsResponse
    `,
  Mutation: `#graphql
    createEditPlaceholderFilterValues(params: CreateEditPlaceholderFilterValuesRequest): Boolean
  `
}

import { inject, injectable } from 'tsyringe'
import PlaceholderService from '../../Application/PlaceholderSlice/PlaceholderService'
import { PlaceholderRequest } from '../../Application/PlaceholderSlice/PlaceholderRequest'
import { IResolver } from '../Common/IResolver'
import { PlaceholderDto } from '../../Application/PlaceholderSlice/PlaceholderDto'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { PlaceholderByFiltersRequest } from '../../Application/PlaceholderSlice/PlaceholderByFiltersRequest'
import { GetPlaceholderFilterValue } from '../../Domain/PlaceholderSlice/GetPlaceholderFilterValue'
import { PlaceholderFilterValues } from '../../Domain/PlaceholderSlice/PlaceholderFilterValues'
import { CreateEditPlaceholderFilterValuesRequest } from '../../Domain/PlaceholderSlice/CreateEditPlaceholderFilterValuesRequest'
import { PlaceholderByWorkCode } from '../../Domain/ResourceSlice/PlaceholderByWorkCode'
import ResourcePlatformRepository from '../../Infrastructure/ResourceSlice/ResourcePlatformRepository'
import { GetOrgStructureByPlaceholderAssignmentsRequest } from '../../Domain/PlaceholderSlice/GetOrgStructureByPlaceholderAssignmentsRequest'
import { GetOrgStructureByAssignmentsResponse } from '../../Domain/PlaceholderSlice/GetOrgStructureByAssignmentsResponse'

@injectable()
export class PlaceholderResolver implements IResolver {
  constructor(
    @inject('PlaceholderService')
    private placeholderService: PlaceholderService,
    private resourcesService: ResourcePlatformRepository
  ) {}

  getQueries() {
    return {
      placeholders: (_, { params }) => this.placeholders(params),
      placeholderFilters: (_, { params }) => this.placeholderFilters(params),
      getPlaceholderFilterValuesByUserId: (_, { params }) => this.getPlaceholderFilterValuesByUserId(params),
      getPlaceholderByWorkCode: (_, { workCode }) => this.getPlaceholderByWorkCode(workCode),
      getOrgStructureByPlaceholderAssignments: (_, { params }) => this.getOrgStructureByPlaceholderAssignments(params)
    }
  }

  getMutations() {
    return {
      createEditPlaceholderFilterValues: (_, { params }) => this.createEditPlaceholderFilterValues(params)
    }
  }

  private async placeholders(params: PlaceholderRequest): Promise<PagingResult<PlaceholderDto>> {
    return await this.placeholderService.getPlaceholders(params)
  }

  private async placeholderFilters(params: PlaceholderByFiltersRequest): Promise<PagingResult<PlaceholderDto>> {
    return await this.placeholderService.getPlaceholdersByFilters(params)
  }

  private async getPlaceholderFilterValuesByUserId(
    params: GetPlaceholderFilterValue
  ): Promise<PlaceholderFilterValues> {
    return await this.placeholderService.getPlaceholderFilterValuesByUserId(params)
  }

  private async createEditPlaceholderFilterValues(params: CreateEditPlaceholderFilterValuesRequest) {
    return await this.placeholderService.createEditPlaceholderFilterValues(params)
  }

  private async getPlaceholderByWorkCode(workCode: string): Promise<PlaceholderByWorkCode[]> {
    return await this.resourcesService.getPlaceholderByWorkCode(workCode)
  }

  private async getOrgStructureByPlaceholderAssignments(
    params: GetOrgStructureByPlaceholderAssignmentsRequest
  ): Promise<GetOrgStructureByAssignmentsResponse> {
    return await this.placeholderService.getOrgStructureByPlaceholderAssignments(params)
  }
}

// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IUsersGroupRepository } from '../../Application/UsersGroupsSlice/IUsersGroupRepository'
import UsersGroupRepository from '../../Infrastructure/UsersGroupsSlice/UsersGroupRepository'

// Service imports
import UsersGroupService from '../../Application/UsersGroupsSlice/UsersGroupService'
import { UsersGroupResolver } from './UsersGroupResolver'

container.register<UsersGroupResolver>('UsersGroupResolver', {
  useClass: UsersGroupResolver
})

container.register<IUsersGroupRepository>('IUsersGroupRepository', {
  useClass: UsersGroupRepository
})
container.register<UsersGroupService>('UsersGroupService', {
  useClass: UsersGroupService
})

import { inject, injectable } from 'tsyringe'
import UsersGroupService from '../../Application/UsersGroupsSlice/UsersGroupService'
import { UsersGroupRequest } from '../../Application/UsersGroupsSlice/UsersGroupRequest'
import { UsersGroup } from '../../Domain/UsersGroupsSlice/UsersGroup'
import { IResolver } from '../Common/IResolver'

@injectable()
export class UsersGroupResolver implements IResolver {
  constructor(
    @inject('UsersGroupService')
    private usersGroupsService: UsersGroupService
  ) {}

  getQueries() {
    return {
      usersGroups: (_, { params }) => this.usersGroups(params)
    }
  }

  getMutations() {
    return {}
  }

  private async usersGroups(params: UsersGroupRequest): Promise<UsersGroup[]> {
    return await this.usersGroupsService.getUsersGroups(params)
  }
}

/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
      type UsersGroup {
        id: Int
        name: String
        userGroupId: String
        createdOn: String
        lastModifiedOn: String
      }
      input UsersGroupParams {
        pageSize: Int!
        pageNumber: Int!
        resourceManagerId: String!
      }
    `,
  Query: `#graphql
       usersGroups(params: UsersGroupParams): [UsersGroup]
    `
}

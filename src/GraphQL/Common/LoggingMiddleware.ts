import ILogger from '../../Domain/Common/ILogger'
import { GraphQLRequestContext } from '@apollo/server'

const loggingMiddleware = (logger: ILogger) => {
  return {
    async requestDidStart(requestContext: GraphQLRequestContext<unknown>) {
      const { request } = requestContext
      logger.info('Request started', { query: request.query, variables: request.variables })

      return {
        async willSendResponse(responseContext: GraphQLRequestContext<unknown>) {
          const { response } = responseContext
          logger.info('Response sent', { response })
        }
      }
    }
  }
}

export default loggingMiddleware

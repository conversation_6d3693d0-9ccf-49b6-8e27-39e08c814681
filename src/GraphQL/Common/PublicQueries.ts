export enum PublicQueryNames {
  GET_WORKFRONT_USER = 'getWorkfrontUser',
  GET_USER_BY_LLID = 'getUserByLLID'
}

export class PublicQueriesConfig {
  private static readonly publicQueries: ReadonlyArray<string> = [
    PublicQueryNames.GET_WORKFRONT_USER,
    PublicQueryNames.GET_USER_BY_LLID
  ]

  /**
   * Check if a GraphQL query is public (doesn't require authentication)
   * @param query - The GraphQL query string
   * @returns true if the query is public, false otherwise
   */
  static isPublicQuery(query: string): boolean {
    if (!query) {
      return false
    }

    return this.publicQueries.some((publicQuery: string) => query.includes(publicQuery))
  }

  /**
   * Get all public query names
   * @returns Array of public query names
   */
  static getPublicQueries(): ReadonlyArray<string> {
    return this.publicQueries
  }

  /**
   * Add a new public query (for future extensibility)
   * Note: This would require updating the enum as well
   * @param queryName - Name of the query to add
   */
  static addPublicQuery(queryName: string): void {
    console.warn(
      `Adding public query dynamically is not recommended. Please update PublicQueryNames enum and publicQueries array to include: ${queryName}`
    )
  }
}

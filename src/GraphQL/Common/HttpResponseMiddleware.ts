import { ApolloServerPlugin, GraphQLResponse } from '@apollo/server'
import CustomApolloError from './CustomErrors/CustomApolloError'

const DEFAULT_ERROR_HTTP_STATUS_CODE = 500

const httpResponseMiddleware: ApolloServerPlugin = {
  async requestDidStart() {
    return {
      async willSendResponse({ response, errors }) {
        if (errors) {
          let httpStatusCode = DEFAULT_ERROR_HTTP_STATUS_CODE
          const customError = errors.find((error) => error.originalError instanceof CustomApolloError)
            ?.originalError as CustomApolloError

          if (customError) {
            httpStatusCode = customError.httpStatusCode
          }

          setResponseHeaders(response, httpStatusCode)
        }
      }
    }
  }
}

// Helper function to set response headers
const setResponseHeaders = (response: GraphQLResponse, status: number) => {
  if (response.http) {
    response.http.status = status
  }
}

export default httpResponseMiddleware

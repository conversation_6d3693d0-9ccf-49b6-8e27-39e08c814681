/**
 * Standard error codes used throughout the GraphQL API
 */

export enum GraphQLErrorCodes {
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}

export enum HttpStatusCodes {
  OK = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500
}

export class ErrorFactory {
  static createAuthorizationError(message = 'Authorization required'): { message: string; code: string } {
    return {
      message,
      code: HttpStatusCodes.NOT_FOUND.toString()
    }
  }

  static createAuthenticationError(message = 'Authentication required'): { message: string; code: string } {
    return {
      message,
      code: HttpStatusCodes.UNAUTHORIZED.toString()
    }
  }

  static createValidationError(message = 'Validation failed'): { message: string; code: string } {
    return {
      message,
      code: HttpStatusCodes.BAD_REQUEST.toString()
    }
  }
}

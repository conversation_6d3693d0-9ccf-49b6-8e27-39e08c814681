import { ApolloServerErrorCode, unwrapResolverError } from '@apollo/server/errors'
import { GraphQLError, GraphQLFormattedError } from 'graphql'
import CustomApolloError from './CustomErrors/CustomApolloError'
import ILogger from '../../Domain/Common/ILogger'

/**
 * Handles errors by formatting them into a GraphQLFormattedError.
 * Logs the error using the provided logger.
 *
 * @param err - The error to handle, which can be of any type.
 * @param logger - An instance of ILogger for logging error details.
 * @returns A GraphQLFormattedError containing the error message and extensions.
 */
export const handleError = (err: unknown, logger: ILogger): GraphQLFormattedError => {
  const { message, code, httpStatusCode, additionalProperties, internalError } = extractErrorDetails(err)

  // Log the error with its code and message for debugging purposes
  logger.error(`An error occurred: ${code} - ${message}`, { error: internalError ?? err })
  // Return a formatted error response to the client
  return {
    message,
    extensions: {
      code,
      additionalProperties,
      innerError: {
        message: internalError?.message
      },
      // Ensure that the httpStatusCode property is only included in the final object if httpStatusCode has a meaningful value
      ...(httpStatusCode && { httpStatusCode })
    }
  }
}

// Helper function to extract error details
const extractErrorDetails = (
  err: unknown
): {
  message: string
  code: string
  httpStatusCode?: number
  additionalProperties?: { [key: string]: unknown }
  internalError?: Error
} => {
  // Attempt to unwrap the resolver error to check if it's a CustomApolloError
  const customError = unwrapResolverError(err)
  const DEFAULT_ERROR_CODE = ApolloServerErrorCode.INTERNAL_SERVER_ERROR
  const DEFAULT_ERROR_MESSAGE = 'An unexpected error occurred.'

  // If the error is a CustomApolloError, extract its details
  if (customError instanceof CustomApolloError) {
    return {
      message: customError.message,
      code: customError.extensions.code as string,
      httpStatusCode: customError.httpStatusCode,
      additionalProperties: customError.additionalProperties,
      internalError: customError.internalError
    }
  }

  if (err instanceof GraphQLError) {
    return {
      message: err.message || DEFAULT_ERROR_MESSAGE,
      code: err.extensions?.code?.toString() || DEFAULT_ERROR_CODE,
      httpStatusCode: 500
    }
  }

  if (err instanceof Error) {
    return {
      message: err.message || DEFAULT_ERROR_MESSAGE,
      code: DEFAULT_ERROR_CODE,
      httpStatusCode: 500
    }
  }
}

/**
 * Authentication configuration for GraphQL context
 */
import { jwtDecode } from 'jwt-decode'

export interface AuthTokenData {
  llid: string
  email: string
  name: string
  externalId: string
  token: string
  profile: string
}

export interface ContextConfig {
  listen: {
    port: number
  }
  container: unknown
}

/**
 * Configuration constants for authentication
 */
export class AuthConfig {
  static readonly defaultPort = 4000
  static readonly jwtUniqueNameSeparator = '@'
  static readonly bearerPrefix = 'Bearer '

  /**
   * Extract user data from JWT token
   * @param token - JWT token string
   * @param profile - User profile from headers
   * @returns Parsed user session data
   */
  static parseJwtToken(token: string, profile?: string): Omit<AuthTokenData, 'token'> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const decodedJWT: any = jwtDecode(token)

    return {
      llid: decodedJWT.unique_name.split(this.jwtUniqueNameSeparator)[0],
      email: decodedJWT.unique_name,
      name: decodedJWT.name,
      externalId: '',
      profile: profile || ''
    }
  }

  /**
   * Create context configuration
   * @param container - Dependency injection container
   * @param port - Server port (optional)
   * @returns Context configuration object
   */
  static createContextConfig(container: unknown, port: number = this.defaultPort): ContextConfig {
    return {
      listen: { port },
      container
    }
  }
}

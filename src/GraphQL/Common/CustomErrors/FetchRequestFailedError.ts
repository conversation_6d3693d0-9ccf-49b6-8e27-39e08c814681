import ILogger from '../../../Domain/Common/ILogger'
import CustomApolloError from './CustomApolloError'
import { CustomError } from './CustomError'
import { inject, injectable } from 'tsyringe'

@injectable()
class FetchRequestFailedError extends CustomApolloError {
  private httpStatusCodeValue: number

  constructor(
    @inject('ILogger') private logger: ILogger,
    internalError?: CustomError,
    message?: string,
    additionalProperties?: { [key: string]: unknown }
  ) {
    // Read status from either response.statusCode or status, fallback to 502
    const status = internalError?.extensions?.status ?? internalError?.extensions?.response?.statusCode ?? 502

    // Set a custom message for 404, otherwise use detail/status/message
    const errorMessage =
      status === 404
        ? 'If you are looking for projects, please check if the project manager has any projects assigned.'
        : (internalError?.extensions?.response?.body?.detail ??
          internalError?.extensions?.status?.toString() ??
          message)

    super(internalError, errorMessage, 'FETCH_REQUEST_ERROR', additionalProperties)

    this.httpStatusCodeValue = status

    // Log the error
    this.logger.error('FetchRequestFailedError: ', {
      internalError,
      message: errorMessage,
      additionalProperties
    })
  }

  get httpStatusCode(): number {
    return this.httpStatusCodeValue
  }
}

export default FetchRequestFailedError

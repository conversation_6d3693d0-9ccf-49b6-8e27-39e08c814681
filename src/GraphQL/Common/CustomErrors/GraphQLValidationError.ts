// Import the CustomError class
import CustomApolloError from './CustomApolloError'
import { ApolloServerErrorCode } from '@apollo/server/errors'

// Define a subclass that extends CustomError
class GraphQLValidationError extends CustomApolloError {
  constructor(internalError?: Error, message?: string) {
    const errorMessage = message ?? 'The query is syntactically correct but fails to validate against the schema.'
    super(internalError, errorMessage, ApolloServerErrorCode.GRAPHQL_VALIDATION_FAILED)
  }

  // Implement the abstract getter to return a specific HTTP status code
  get httpStatusCode(): number {
    return 400
  }
}

export default GraphQLValidationError

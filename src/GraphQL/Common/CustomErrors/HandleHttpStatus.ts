export class HttpStatusError extends Error {
  status: number
  constructor(status: number, message?: string) {
    super(message || `HTTP Error: ${status}`)
    this.status = status
    this.name = 'HttpStatusError'
  }
}

export async function handleHttpStatus<T extends { status?: number }>(fetchPromise: Promise<T>): Promise<T> {
  const response = await fetchPromise
  if (response && typeof response.status === 'number' && response.status >= 400) {
    throw new HttpStatusError(response.status)
  }
  return response
}

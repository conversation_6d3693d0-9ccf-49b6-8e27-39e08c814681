// Import the CustomError class
import CustomApolloError from './CustomApolloError'
import { ApolloServerErrorCode } from '@apollo/server/errors'

// Define a subclass that extends CustomError
class PersistedQueryNotSupportedError extends CustomApolloError {
  constructor(message?: string, internalError?: Error) {
    const errorMessage = message ?? 'The query is syntactically correct but fails to validate against the schema.'
    super(internalError, errorMessage, ApolloServerErrorCode.PERSISTED_QUERY_NOT_SUPPORTED)
  }

  // Implement the getter for HTTP status code
  get httpStatusCode(): number {
    return 422
  }
}

export default PersistedQueryNotSupportedError

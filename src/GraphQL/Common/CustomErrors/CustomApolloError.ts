import { ApolloServerErrorCode } from '@apollo/server/errors'
import { GraphQLError } from 'graphql'

abstract class CustomApolloError extends GraphQLError {
  errorCode: string
  internalError: Error
  additionalProperties: { [key: string]: unknown }

  constructor(error?: Error, message?: string, code?: string, additionalProperties?: { [key: string]: unknown }) {
    const errorMessage = message ?? 'An unexpected error occurred'

    super(errorMessage, { extensions: { code: code ?? ApolloServerErrorCode.INTERNAL_SERVER_ERROR } })

    // Save the internal error. In case of no internal error, save the default error.
    this.internalError = error

    // Save the additional properties as string
    this.additionalProperties = additionalProperties
  }

  // Must implement getter of the httpStatusCode
  abstract get httpStatusCode(): number

  // Display a string with the additional properties
  public additionalPropertiesAsString(): string {
    return Object.keys(this.additionalProperties)
      .map((key) => `${key}: ${this.additionalProperties[key]}`)
      .join(' | ')
  }
}

export default CustomApolloError

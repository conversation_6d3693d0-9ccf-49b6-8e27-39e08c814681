// Import the CustomError class
import CustomApolloError from './CustomApolloError'
import { ApolloServerErrorCode } from '@apollo/server/errors'

// Define a subclass that extends CustomError
class BadRequestError extends CustomApolloError {
  constructor(internalError?: Error, message?: string) {
    const errorMessage = message ?? 'The request could not be understood by the server due to malformed syntax.'
    super(internalError, errorMessage, ApolloServerErrorCode.BAD_REQUEST)
  }

  // Implement the abstract getter to return a specific HTTP status code
  get httpStatusCode(): number {
    return 400 // HTTP status code for bad request
  }
}

export default BadRequestError

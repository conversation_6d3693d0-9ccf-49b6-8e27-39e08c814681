// Injector imports
import { container } from 'tsyringe'
import { ICacheService } from '../../Application/Common/ICacheService'
import CacheService from '../../Infrastructure/Common/CacheService'
import Logger from './Logger'
import ILogger from '../../Domain/Common/ILogger'

container.registerSingleton<ICacheService>('ICacheService', CacheService)
container.registerSingleton<ILogger>('ILogger', <PERSON><PERSON>)

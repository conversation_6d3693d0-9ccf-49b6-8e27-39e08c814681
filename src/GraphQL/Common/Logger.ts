import { container, injectable } from 'tsyringe'
import winston from 'winston'
import { AzureApplicationInsightsLogger, ApplicationInsightsTransport } from 'winston-azure-application-insights'
import appInsights from 'applicationinsights'
import ILogger from '../../Domain/Common/ILogger'
import { config } from '../../Config/environment'

@injectable()
class Logger implements ILogger {
  private logger: winston.Logger

  constructor() {
    const isAzureEnvironment = config.websiteName !== undefined
    const instrumentationKey = config.azureInstrumentationKey

    const transports: winston.transport[] = []

    if (isAzureEnvironment && instrumentationKey) {
      appInsights
        .setup(instrumentationKey)
        .setAutoCollectRequests(true)
        .setAutoCollectPerformance(true, true)
        .setAutoCollectExceptions(true)
        .setAutoCollectConsole(true, true)
        .setAutoCollectPreAggregatedMetrics(true)
        .setSendLiveMetrics(false)
        .setInternalLogging(false, true)
        .enableWebInstrumentation(false)
        .start()
      transports.push(
        new AzureApplicationInsightsLogger({
          insights: appInsights.defaultClient
        })
      )

      transports.push(
        new ApplicationInsightsTransport({
          instrumentationKey: instrumentationKey
        })
      )
    } else {
      transports.push(new winston.transports.Console())
    }

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.json(),
      defaultMeta: { service: 'apollo-server' },
      transports
    })
  }

  info(message: string, meta?: Record<string, unknown>) {
    this.logger.info(message, meta)
  }

  error(message: string, meta?: Record<string, unknown>) {
    this.logger.error(message, meta)
  }

  warn(message: string, meta?: Record<string, unknown>) {
    this.logger.info(message, meta)
  }

  debug(message: string, meta?: Record<string, unknown>) {
    this.logger.error(message, meta)
  }
}

container.register('Logger', Logger)

export default Logger

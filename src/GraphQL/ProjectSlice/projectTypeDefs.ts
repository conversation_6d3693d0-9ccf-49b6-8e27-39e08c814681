/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
    type Project {
      id: ID
      name: String
      brandId: String
      clientId: String
      startDate: String
      endDate: String
      projectManagerCode: String
      integrationId: String
      costCenterCode: String
      agencyCode: String
    }
    type ProjectRM {
      id: ID
      name: String
      integrationId: String
      brandId: String
    }
  `,

  Query: `#graphql
    getProjectsByProjectManagerWorkCode(workCode:String): [Project]
    getProjectsByResourceManagerId(workCode:String,isExpandedPlaceholderApplied:Boolean): [ProjectRM]
    getProjectsByProjectId(projectId:String): [Project]
  `
}

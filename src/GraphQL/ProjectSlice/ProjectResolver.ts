import { inject, injectable } from 'tsyringe'
import ProjectService from '../../Application/ProjectSlice/ProjectService'

import { IResolver } from '../Common/IResolver'
import { ProjectRM } from '../../Domain/ProjectSlice/ProjectRM'
import { ProjectPM } from '../../Domain/ProjectSlice/ProjectPM'
import { Project } from '../../Domain/ProjectSlice/Project'

@injectable()
export class ProjectResolver implements IResolver {
  constructor(
    @inject('ProjectService')
    private projectService: ProjectService
  ) {}

  getQueries() {
    return {
      getProjectsByProjectManagerWorkCode: (_, { workCode }) => this.getProjectsByProjectManagerWorkCode(workCode),
      getProjectsByResourceManagerId: (_, { workCode, isExpandedPlaceholderApplied }) =>
        this.getProjectsByResourceManagerId(workCode, isExpandedPlaceholderApplied),
      getProjectsByProjectId: (_, { projectId }) => this.getProjectsByProjectId(projectId)
    }
  }

  getMutations() {
    return {}
  }

  private async getProjectsByProjectManagerWorkCode(workCode: string): Promise<ProjectPM[]> {
    return await this.projectService.getProjectsByProjectManagerWorkCode(workCode)
  }

  private async getProjectsByResourceManagerId(
    workCode: string,
    isExpandedPlaceholderApplied: boolean = false
  ): Promise<ProjectRM[]> {
    return await this.projectService.getProjectsByResourceManagerId(workCode, isExpandedPlaceholderApplied)
  }

  private async getProjectsByProjectId(projectId: string): Promise<Project> {
    return await this.projectService.getProjectsByProjectId(projectId)
  }
}

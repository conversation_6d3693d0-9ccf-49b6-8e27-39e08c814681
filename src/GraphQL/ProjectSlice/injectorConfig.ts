// Injector imports
import { container } from 'tsyringe'

// Service imports
import { ProjectResolver } from './ProjectResolver'
import { IProjectRepository } from '../../Application/ProjectSlice/IProjectRepository'
import ProjectRepository from '../../Infrastructure/ProjectSlice/ProjectRepository'
import ProjectService from '../../Application/ProjectSlice/ProjectService'
import { IProjectRMRepository } from '../../Application/ProjectSlice/IProjectRMRepository'
import ProjectRMRepository from '../../Infrastructure/ProjectSlice/ProjectRMRepository'

container.register<ProjectResolver>('ProjectResolver', {
  useClass: ProjectResolver
})

container.register<IProjectRepository>('IProjectRepository', {
  useClass: ProjectRepository
})
container.register<ProjectService>('ProjectService', {
  useClass: ProjectService
})

container.register<IProjectRMRepository>('IProjectRMRepository', {
  useClass: ProjectRMRepository
})

import { inject, injectable } from 'tsyringe'
import { CalendarService } from '../../Application/CalendarSlice/CalendarService'
import { CalendarRequest } from '../../Application/CalendarSlice/CalendarRequest'
import { MonthData } from '../../Domain/CalendarSlice/MonthData'
import { IResolver } from '../Common/IResolver'

@injectable()
export class CalendarResolver implements IResolver {
  constructor(@inject('CalendarService') private calendarService: CalendarService) {}

  getQueries() {
    return {
      getCalendarData: (_, { params }) => this.getCalendarData(params)
    }
  }

  getMutations() {
    return {}
  }

  private async getCalendarData(request: CalendarRequest): Promise<MonthData[]> {
    return await this.calendarService.getCalendarData(request)
  }
}

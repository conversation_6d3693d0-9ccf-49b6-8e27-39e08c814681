/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
    type Calendar {
      month: String
      days: [Day]
    }

    type Day {
      dayShortName: String
      dayMiddleName: String
      dayNumber: Int
      date: String
      isWeekend: Boolean
    }

    input CalendarParams {
      startDate: String!
      endDate: String!
    }
  `,
  Query: `#graphql
    getCalendarData(params: CalendarParams): [Calendar]
  `
}

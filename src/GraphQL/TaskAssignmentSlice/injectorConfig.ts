// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IAssignmentRepository } from '../../Application/TaskAssignmentSlice/IAssignmentRepository'
import AssignmentRepository from '../../Infrastructure/TaskAssignmentSlice/AssignmentRepository'

// Service imports
import AssignmentService from '../../Application/TaskAssignmentSlice/AssignmentService'
import { AssignmentResolver } from './TaskAssignmentResolver'

container.register<AssignmentResolver>('AssignmentResolver', {
  useClass: AssignmentResolver
})

container.register<IAssignmentRepository>('IAssignmentRepository', {
  useClass: AssignmentRepository
})
container.register<AssignmentService>('AssignmentService', {
  useClass: AssignmentService
})

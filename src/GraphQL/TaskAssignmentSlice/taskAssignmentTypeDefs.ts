/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
    type NonWorkingDate {
      date: String!
      reason: NonWorkingDayReason!
      isWorking: Boolean!
    }

    enum NonWorkingDayReason {
      WeekEnd
      Holiday
      Absence
    }
    
    type Assignment {
      id: ID
      startDate: String
      dueDate: String
      hoursPerDay: Float
      state: String
      assignmentState: String
      userId: String
      projectId: String
      taskId: String
      nonWorkingDates: [NonWorkingDate]
      totalDays: Int
      totalWorkingDays: Int
      x: Int
      y: Int
      width: Int
      height: Int
      isPlaceholder: Boolean
      taskName: String
      projectName: String
      resourceId: String
      externalLink: String
      altairJobNumber: String
      projectManagerName: String
      taskState: String
      taskNotes: String
      assignmentNotes: String
      taskStartDate: String
      taskDueDate: String
      projectAgencyCode: String
      projectBrandCode: String
      projectAgencyName: String
      projectIntegrationId: String
      userIntegrationId: String
      agencyCode: String
      agencyName:String
      costCenterName: String
      costCenterCode: String
      locationName: String
      blockedByTimesheet: Boolean
      assignmentIcaStatus: String
    }
    input AssignmentParams {
      startDate: String!
      endDate: String!
      workCode: String!
      userIds: [String]
      projectIds: [String]
      taskIds: [String]
      agencyCode: String
      costCenterCode: String
      location: String
      brandIds: [String]      
      taskStates: [String]
      usersGroupIds: [String]
      userLoggedInExternalId: String
      isExpandedPlaceholderApplied: Boolean
    }
    input AssignmentInput {
      id: ID
      startDate: String
      dueDate: String
      hoursPerDay: Int
      resourceId: String
      x: Int
      y: Int
      width: Int
      height: Int
      # status: String
      # projectId: String
      # taskId: String
      # activeNonWorkingDays: [String]
    }
    
    input CreateAssignmentParams {
      taskExternalId: String
      userAssignedExternalId: String
      userLoggedInExternalId: String
      startDate: String
      dueDate: String
      hoursPerDay: Float
      calendarStartDate: String
      calendarDueDate: String,
      isSplit: Boolean
      createAssignmentDate: String
      agencyName: String
      locationName: String
      costCenterName: String
      agencyCode: String
      costCenterCode: String
      nonWorkingDates: [NonWorkingDateInput]
    }

    input UpdateAssignmentParams {
      externalId: ID
      startDate: String
      dueDate: String
      hoursPerDay: Float
      userId: String
      userLoggedInExternalId: String
      updateAssignmentDate: String
      agencyName: String
      locationName: String
      costCenterName: String
      agencyCode: String
      costCenterCode: String
      isSplit: Boolean
      nonWorkingDates: [NonWorkingDateInput]
    }

    input ApproveRejectAssignment{
      externalId: String!
      taskAssignmentState: Int!
      reason:String
      userLoggedInExternalId: String!
      role: String
    }

    input NonWorkingDateInput {
      date: String!
      reason: NonWorkingDayReason!
      isWorking: Boolean!
    }
  `,
  Query: `#graphql
    assignments(params: AssignmentParams): [Assignment]
    getAssignmentById(assignmentId: ID): Assignment
  `,
  Mutation: `#graphql
    updateAssignment(assignment: UpdateAssignmentParams): Boolean
    createAssignment(params: CreateAssignmentParams): Assignment
    deleteAssignmentById(assignmentId: String, userLoggedInExternalId: String): Boolean
    approveRejectAssignment(params: ApproveRejectAssignment):Boolean
  `
}

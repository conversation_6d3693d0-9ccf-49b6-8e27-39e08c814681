import { inject, injectable } from 'tsyringe'
import AssignmentService from '../../Application/TaskAssignmentSlice/AssignmentService'
import { AssignmentRequest } from '../../Application/TaskAssignmentSlice/AssignmentRequest'
import { Assignment } from '../../Domain/TaskAssignmentSlice/Assignment'
import { IResolver } from '../Common/IResolver'
import { CreateAssignmentRequestMutation } from '../../Application/TaskAssignmentSlice/CreateAssignmentRequest'
import { UpdateAssignmentRequest } from '../../Domain/TaskAssignmentSlice/UpdateAssignmentRequest'
import { ApproveRejectAssignment } from '../../Domain/TaskAssignmentSlice/ApproveRejectAssignment'

@injectable()
export class AssignmentResolver implements IResolver {
  constructor(
    @inject('AssignmentService')
    private assignmentsService: AssignmentService
  ) {}

  getQueries() {
    return {
      assignments: (_, { params }) => this.assignments(params),
      getAssignmentById: (_, { assignmentId }) => this.getAssignmentById(assignmentId)
    }
  }

  getMutations() {
    return {
      updateAssignment: (_, { assignment }) => this.updateAssignment(assignment),
      createAssignment: (_, { params }) => this.createAssignment(params),
      deleteAssignmentById: (_, { assignmentId, userLoggedInExternalId }) =>
        this.deleteAssignmentById(assignmentId, userLoggedInExternalId),
      approveRejectAssignment: (_, { params }) => this.approveRejectAssignment(params)
    }
  }

  private async assignments(params: AssignmentRequest): Promise<Assignment[]> {
    return await this.assignmentsService.getAssignments(params)
  }

  private async getAssignmentById(assignmentId: string): Promise<Assignment> {
    return await this.assignmentsService.getAssignmentById(assignmentId)
  }

  private async updateAssignment(assignment: UpdateAssignmentRequest): Promise<boolean> {
    return await this.assignmentsService.updateAssignment(assignment)
  }

  private async createAssignment(params: CreateAssignmentRequestMutation) {
    return await this.assignmentsService.createAssignment(params)
  }

  private async deleteAssignmentById(assignmentId: string, userLoggedInExternalId: string): Promise<boolean> {
    return await this.assignmentsService.deleteAssignmentById(assignmentId, userLoggedInExternalId)
  }

  private async approveRejectAssignment(params: ApproveRejectAssignment): Promise<boolean> {
    return await this.assignmentsService.approveRejectAssignment(params)
  }
}

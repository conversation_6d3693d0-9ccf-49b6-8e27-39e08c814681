// Injector imports
import 'reflect-metadata'
import { container } from 'tsyringe'

// Repository imports
import { IResourceAndPlaceholderRepository } from '../../Application/ResourceAndPlaceholderSlice/IResourceAndPlaceholderRepository'
import ResourceAndPlaceholderRepository from '../../Infrastructure/ResourceAndPlaceholderSlice/ResourceAndPlaceholderRepository'

// Service imports
import ResourceAndPlaceholderService from '../../Application/ResourceAndPlaceholderSlice/ResourceAndPlaceholderService'
import { ResourceAndPlaceholderResolver } from './ResourceAndPlaceholderResolver'

container.register<ResourceAndPlaceholderResolver>('ResourceAndPlaceholderResolver', {
  useClass: ResourceAndPlaceholderResolver
})

container.register<IResourceAndPlaceholderRepository>('IResourceAndPlaceholderRepository', {
  useClass: ResourceAndPlaceholderRepository
})

container.register<ResourceAndPlaceholderService>('ResourceAndPlaceholderService', {
  useClass: ResourceAndPlaceholderService
})

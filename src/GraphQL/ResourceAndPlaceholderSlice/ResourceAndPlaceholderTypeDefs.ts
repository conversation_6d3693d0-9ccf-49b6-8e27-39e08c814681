/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
    type ResourceAndPlaceholder {
      id: String
      name: String
      isPlaceholder: Boolean
      integrationId: String
    }

    input ResourceAndPlaceholderParams {
      userId: String
      searchName: String
    }
  `,
  Query: `#graphql    
    getResourceAndPlaceholder(params: ResourceAndPlaceholderParams): [ResourceAndPlaceholder]
  `
}

// src/GraphQL/UsersSlice/UserResolver.ts
import { inject, injectable } from 'tsyringe'
import { IResolver } from '../Common/IResolver'
import { ResourceAndPlaceholderRequest } from '../../Application/ResourceAndPlaceholderSlice/ResourceAndPlaceholderRequest'
import { ResourceAndPlaceholderDto } from '../../Application/ResourceAndPlaceholderSlice/ResourceAndPlaceholderDto'
import ResourceAndPlaceholderService from '../../Application/ResourceAndPlaceholderSlice/ResourceAndPlaceholderService'

@injectable()
export class ResourceAndPlaceholderResolver implements IResolver {
  constructor(
    @inject('ResourceAndPlaceholderService')
    private resourceAndPlaceholderService: ResourceAndPlaceholderService
  ) {}

  getQueries() {
    return {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getResourceAndPlaceholder: (_, { params }) => this.getResourceAndPlaceholder(params)
    }
  }

  getMutations() {
    return {}
  }

  private async getResourceAndPlaceholder(params: ResourceAndPlaceholderRequest): Promise<ResourceAndPlaceholderDto[]> {
    return await this.resourceAndPlaceholderService.getResourcesAndPlaceholders(params)
  }
}

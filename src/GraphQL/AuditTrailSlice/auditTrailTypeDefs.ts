/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
        type AuditDto {
            id: String
            date: String
            time: String
            action: String
            details: AuditDetails
        }
  
          type AuditDetails {
            oldAssignment: AuditLogTaskAssignmentDto
            newAssignment: AuditLogTaskAssignmentDto
        }

        type AuditLogTaskAssignmentDto {
            externalId: String
            hoursPerDay: Float
            startDate: String
            dueDate: String
            assignmentStatus: String
            resourceName: String
            resourceId: String
            taskName: String
            projectName: String
            isPlaceholder: Boolean
        }
  
      `,

  Query: `#graphql
    getAuditTrailForLoggedInUser(userLoggedInExternalId:String): [AuditDto]
  `,
  Mutation: `#graphql
    clearAuditTrailForLoggedInUser(userLoggedInExternalId:String): Boolean
  `
}

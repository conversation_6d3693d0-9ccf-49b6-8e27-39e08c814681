import { inject, injectable } from 'tsyringe'
import { IResolver } from '../Common/IResolver'
import AuditTrailService from '../../Application/AuditTrailSlice/AuditTrailService'
import { AuditDto } from '../../Application/AuditTrailSlice/AuditDto'

@injectable()
export class AuditTrailResolver implements IResolver {
  constructor(
    @inject('AuditTrailService')
    private auditTrailService: AuditTrailService
  ) {}

  getQueries() {
    return {
      getAuditTrailForLoggedInUser: (_, { userLoggedInExternalId }) =>
        this.getAuditTrailForLoggedInUser(userLoggedInExternalId)
    }
  }

  getMutations() {
    return {
      clearAuditTrailForLoggedInUser: (_, { userLoggedInExternalId }) =>
        this.clearAuditTrailForLoggedInUser(userLoggedInExternalId)
    }
  }

  private async getAuditTrailForLoggedInUser(userLoggedInExternalId: string): Promise<AuditDto[]> {
    return await this.auditTrailService.getAuditTrailForLoggedInUser(userLoggedInExternalId)
  }

  private async clearAuditTrailForLoggedInUser(userLoggedInExternalId: string): Promise<boolean> {
    return await this.auditTrailService.clearAuditTrailForLoggedInUser(userLoggedInExternalId)
  }
}

// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IAuditTrailRepository } from '../../Application/AuditTrailSlice/IAuditTrailRepository'
import AuditTrailRepository from '../../Infrastructure/AuditTrailSlice/AuditTrailRepository'

// Service imports
import AuditTrailService from '../../Application/AuditTrailSlice/AuditTrailService'
import { AuditTrailResolver } from './AuditTrailResolver'

container.register<AuditTrailResolver>('AuditTrailResolver', {
  useClass: AuditTrailResolver
})

container.register<IAuditTrailRepository>('IAuditTrailRepository', {
  useClass: AuditTrailRepository
})

container.register<AuditTrailService>('AuditTrailService', {
  useClass: AuditTrailService
})

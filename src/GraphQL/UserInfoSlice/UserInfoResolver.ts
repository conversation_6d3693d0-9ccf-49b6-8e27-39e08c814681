import { inject, injectable } from 'tsyringe'
import { UserInfo } from '../../Domain/UserInfoSlice/UserInfo'
import { IResolver } from '../Common/IResolver'
import UserInfoService from '../../Application/UserInfoSlice/UserInfoService'

@injectable()
export class UserInfoResolver implements IResolver {
  constructor(
    @inject('UserInfoService')
    private userInfoService: UserInfoService
  ) {}

  getQueries() {
    return {
      getUserInfo: () => this.getUserInfo(),
      getUserByLLID: (_, { llid }) => this.getUserByLLID(llid)
    }
  }

  getMutations() {
    return {}
  }

  private async getUserInfo(): Promise<UserInfo> {
    return await this.userInfoService.getUserInfo()
  }

  private async getUserByLLID(llid: string): Promise<UserInfo> {
    return await this.userInfoService.getUserByLLID(llid)
  }
}

// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { IUserInfoRepository } from '../../Application/UserInfoSlice/UserInfoRepository'
import UserInfoRepository from '../../Infrastructure/UserInfoSlice/UserInfoRepository'

// Service imports
import { UserInfoResolver } from './UserInfoResolver'
import UserInfoService from '../../Application/UserInfoSlice/UserInfoService'

container.register<UserInfoResolver>('UserInfoResolver', {
  useClass: UserInfoResolver
})

container.register<IUserInfoRepository>('IUserInfoRepository', {
  useClass: UserInfoRepository
})

container.register<UserInfoService>('UserInfoService', {
  useClass: UserInfoService
})

// Injector imports
import { container } from 'tsyringe'

// Repository imports
import { ITasksRepository } from '../../Application/TasksSlice/ITasksRepository'
import TasksRepository from '../../Infrastructure/TasksSlice/TasksRepository'

// Service imports
import TasksService from '../../Application/TasksSlice/TasksService'
import { TasksResolver } from './TasksResolver'

container.register<TasksResolver>('TasksResolver', {
  useClass: TasksResolver
})

container.register<ITasksRepository>('ITasksRepository', {
  useClass: TasksRepository
})

container.register<TasksService>('TasksService', {
  useClass: TasksService
})

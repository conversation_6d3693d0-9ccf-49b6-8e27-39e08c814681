import { inject, injectable } from 'tsyringe'
import { IResolver } from '../Common/IResolver'
import TasksService from '../../Application/TasksSlice/TasksService'
import { Task } from '../../Domain/TasksSlice/Task'
import { ProjectWithTasks } from '../../Domain/ProjectSlice/ProjectWithTasks'

@injectable()
export class TasksResolver implements IResolver {
  constructor(
    @inject('TasksService')
    private tasksService: TasksService
  ) {}

  getQueries() {
    return {
      getTasksByProjectId: (_, { projectId }) => this.getTasksByProjectId(projectId),
      getPMTasksByGroupOfProjects: (_, { projectIds, workCode }) =>
        this.getPMTasksByGroupOfProjects(projectIds, workCode),
      getRMTasksByGroupOfProjects: (_, { projectIds, workCode }) =>
        this.getRMTasksByGroupOfProjects(projectIds, workCode)
    }
  }

  getMutations() {
    return {}
  }

  private async getTasksByProjectId(projectId: string): Promise<Task[]> {
    return await this.tasksService.getTasksByProjectId(projectId)
  }

  private async getPMTasksByGroupOfProjects(projectIds: string[], workCode: string): Promise<ProjectWithTasks[]> {
    return await this.tasksService.getPMTasksByGroupOfProjects(projectIds, workCode)
  }

  private async getRMTasksByGroupOfProjects(projectIds: string[], workCode: string): Promise<ProjectWithTasks[]> {
    return await this.tasksService.getRMTasksByGroupOfProjects(projectIds, workCode)
  }
}

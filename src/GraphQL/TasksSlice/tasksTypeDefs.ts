/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Schema: `#graphql
      type Task {
        id: ID
        taskId: String
        taskName: String
        taskStatus: String
        taskStartDate: String
        taskEndDate: String
      }

        type ProjectWithTasks {
        id: String
        name: String
        tasks: [Task]
      }

    `,

  Query: `#graphql
      getTasksByProjectId(projectId:String): [Task]
      getPMTasksByGroupOfProjects(projectIds:[String], workCode:String): [ProjectWithTasks]
      getRMTasksByGroupOfProjects(projectIds:[String], workCode:String): [ProjectWithTasks]
    `
}

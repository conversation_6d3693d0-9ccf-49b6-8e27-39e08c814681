// Resolvers imports
import { container } from 'tsyringe'
import { IResolver } from './Common/IResolver'
import { UserResolver } from './UsersSlice/UserResolver'
import { UsersGroupResolver } from './UsersGroupsSlice/UsersGroupResolver'
import { ResourceResolver } from './ResourceSlice/ResourceResolver'
import { AssignmentResolver } from './TaskAssignmentSlice/TaskAssignmentResolver'
import { CalendarResolver } from './CalendarSlice/CalendarResolver'
import { ProjectResolver } from './ProjectSlice/ProjectResolver'
import { AuthResolver } from './AuthSlice/AuthResolver'
import { PlaceholderResolver } from './PlaceholderSlice/PlaceholderResolver'
import { TasksResolver } from './TasksSlice/TasksResolver'
import { ResourceAndPlaceholderResolver } from './ResourceAndPlaceholderSlice/ResourceAndPlaceholderResolver'
import { BrandResolver } from './BrandSlice/BrandResolver'
import { AuditTrailResolver } from './AuditTrailSlice/AuditTrailResolver'
import { TimesheetsResolver } from './TimesheetsSlice/TimesheetsResolver'
import { WorkfrontResolver } from './WorkfrontSlice/WorkfrontResolver'
import { UserInfoResolver } from './UserInfoSlice/UserInfoResolver'

//TypeDefs imports
import User from './UsersSlice/userTypeDefs'
import Assignment from './TaskAssignmentSlice/taskAssignmentTypeDefs'
import UsersGroup from './UsersGroupsSlice/usersGroupTypeDefs'
import Resource from './ResourceSlice/resourceTypeDefs'
import Calendar from './CalendarSlice/calendarTypeDefs'
import Project from './ProjectSlice/projectTypeDefs'
import Task from './TasksSlice/tasksTypeDefs'
import Auth from './AuthSlice/authTypeDefs'
import Placeholder from './PlaceholderSlice/placeholderTypeDefs'
import ResourceAndPlaceholder from './ResourceAndPlaceholderSlice/ResourceAndPlaceholderTypeDefs'
import BrandSchema from './BrandSlice/BrandTypeDefs'
import AuditTrailSchema from './AuditTrailSlice/auditTrailTypeDefs'
import TimesheetsSchema from './TimesheetsSlice/timesheetsTypeDefs'
import WorkfrontSchema from './WorkfrontSlice/workfrontTypeDefs'
import UserInfoSchema from './UserInfoSlice/userInfoTypeDefs'

// Automatically resolve all resolvers
const resolversArray: IResolver[] = [
  container.resolve(UserResolver),
  container.resolve(UsersGroupResolver),
  container.resolve(ResourceResolver),
  container.resolve(AssignmentResolver),
  container.resolve(CalendarResolver),
  container.resolve(ProjectResolver),
  container.resolve(AuthResolver),
  container.resolve(PlaceholderResolver),
  container.resolve(TasksResolver),
  container.resolve(ResourceAndPlaceholderResolver),
  container.resolve(BrandResolver),
  container.resolve(AuditTrailResolver),
  container.resolve(TimesheetsResolver),
  container.resolve(WorkfrontResolver),
  container.resolve(UserInfoResolver)
]

// Combine all queries and mutations
/* eslint-disable @typescript-eslint/naming-convention */
const resolvers = {
  Query: {},
  Mutation: {}
}
/* eslint-enable @typescript-eslint/naming-convention */

for (const resolver of resolversArray) {
  Object.assign(resolvers.Query, resolver.getQueries())
  Object.assign(resolvers.Mutation, resolver.getMutations())
}

// Export the resolvers and typeDefs for Apollo Server
export { resolvers }

export const typeDefs = `#graphql
    ${User.Schema},
    ${Calendar.Schema},
    ${Assignment.Schema},
    ${UsersGroup.Schema},
    ${Resource.Schema},
    ${Project.Schema},
    ${Placeholder.Schema},
    ${Task.Schema},
    ${ResourceAndPlaceholder.Schema},
    ${BrandSchema.Schema},
    ${AuditTrailSchema.Schema},
    ${TimesheetsSchema.Schema},
    ${WorkfrontSchema.Schema},
    ${UserInfoSchema.Schema},

    type Query {
        ${User.Query},
        ${Calendar.Query},
        ${Assignment.Query},
        ${UsersGroup.Query},
        ${Resource.Query},
        ${Project.Query},
        ${Placeholder.Query},
        ${Task.Query},
        ${ResourceAndPlaceholder.Query},
        ${BrandSchema.Query},
        ${AuditTrailSchema.Query},
        ${TimesheetsSchema.Query},
        ${WorkfrontSchema.Query},
        ${UserInfoSchema.Query},
    }
    type Mutation {
        ${Assignment.Mutation},
        ${Auth.Mutation},
        ${AuditTrailSchema.Mutation},
        ${Placeholder.Mutation}
    }
`

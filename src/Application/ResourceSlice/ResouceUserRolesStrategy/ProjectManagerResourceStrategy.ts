import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Resource } from '../../../Domain/ResourceSlice/Resource'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IResourceRepository } from '../IResourceRepository'
import { IResourceStrategy } from './IResourceStrategy'
import { ResourceRequest } from '../ResourceRequest'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import ILogger from '../../../Domain/Common/ILogger'

export class ProjectManagerResourceStrategy implements IResourceStrategy {
  constructor(
    private resourcesRepository: IResourceRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger // Inject ILogger
  ) {}

  async fetchResources(params: ResourceRequest): Promise<PagingResult<Resource>> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userId)
      if (!projects) {
        // No projects found (404 or empty result)
        this.logger.info('No projects found for this project manager (empty result).')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      const projectIds = projects.map((project) => project.projectId)

      // Fetch resources for project manager
      return await this.resourcesRepository.getResourcesForProjectManager(
        params.userId,
        projectIds,
        params.pageNumber,
        params.pageSize,
        params.sort,
        params.searchName
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('No projects found for this project manager.')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      this.logger.error('Error fetching resources for project manager:', error)
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Resource } from '../../../Domain/ResourceSlice/Resource'
import { IResourceStrategy } from './IResourceStrategy'
import { ResourceRequest } from '../ResourceRequest'

export class LightUserResourceStrategy implements IResourceStrategy {
  constructor(private userName: string) {}

  async fetchResources(params: ResourceRequest): Promise<PagingResult<Resource>> {
    // Fetch resource for light user
    return {
      items: [
        {
          externalId: params.userId,
          name: this.userName,
          isPlaceholder: false,
          integrationId: '',
          workCode: '',
          jobTitle: '',
          requiresAssignApproval: false
        }
      ],
      totalCount: 1,
      hasNextPage: false,
      hasPreviousPage: false,
      pageNumber: 1,
      totalPages: 1
    }
  }
}

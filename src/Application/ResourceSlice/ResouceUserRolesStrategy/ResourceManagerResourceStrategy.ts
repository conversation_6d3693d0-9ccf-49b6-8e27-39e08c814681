import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Resource } from '../../../Domain/ResourceSlice/Resource'
import { IResourceRepository } from '../IResourceRepository'
import { IResourceStrategy } from './IResourceStrategy'
import { ResourceRequest } from '../ResourceRequest'

export class ResourceManagerResourceStrategy implements IResourceStrategy {
  constructor(private resourcesRepository: IResourceRepository) {}

  async fetchResources(params: ResourceRequest): Promise<PagingResult<Resource>> {
    // Fetch resources for resource manager
    return await this.resourcesRepository.getResourcesForResourceManager(
      params.userId,
      params.sort.order,
      params.pageNumber,
      params.pageSize,
      params.searchName ?? ''
    )
  }
}

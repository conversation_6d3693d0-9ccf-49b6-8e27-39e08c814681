import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourceRepository } from './IResourceRepository'
import { IResourceStrategy } from './ResouceUserRolesStrategy/IResourceStrategy'
import { ProjectManagerResourceFiltersStrategy } from './ResourceFiltersStrategy/ProjectManagerResourceFiltersStrategy'
import { ResourceManagerResourceFiltersStrategy } from './ResourceFiltersStrategy/ResourceManagerResourceFiltersStrategy'
import ILogger from '../../Domain/Common/ILogger' // Adjust this path as needed based on your project structure

export class ResourceFiltersStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    resourcesRepository: IResourceRepository,
    logger: ILogger // Add logger parameter
  ): IResourceStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerResourceFiltersStrategy(resourcesRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerResourceFiltersStrategy(resourcesRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

import { AgencyRule } from '../../Domain/ResourceSlice/AgencyRule'
import { ResourcePlatform } from '../../Domain/ResourceSlice/ResourcePlatform'
import { Employee } from '../../Domain/ResourceAndPlaceholderSlice/Employee'
import { PlatformOrgStructure } from '../../Domain/ResourceSlice/PlatformOrgStructure'
import { Agency } from '../../Domain/ResourceSlice/Agency'
import { WorkFrontData } from '../../Domain/ResourceSlice/WorkFrontData'
import { Holiday } from '../../Domain/ResourceSlice/Holiday'
import { PlaceholderByWorkCode } from '../../Domain/ResourceSlice/PlaceholderByWorkCode'

export interface IResourcePlatformRepository {
  getResourceById(resourceId: string): Promise<ResourcePlatform>
  getResourcesByWorkCodes(workCodes: string[]): Promise<ResourcePlatform[]>
  getResourceAgencyRules(agencyCode: string): Promise<AgencyRule>
  getResourceAgenciesRules(agencyCode: string[]): Promise<AgencyRule[]>
  getAgenciesData(agencyCodes: string[]): Promise<{ agency: { agencyCode: string; agencyName: string } }[]>
  getUsersBySearchName(searchName: string): Promise<Employee[]>
  getUserOrgStructure(agencyCode: string, costCenterCode: string): Promise<PlatformOrgStructure[]>
  getUserOrgStructureByAgencyCode(agencyCode: string): Promise<PlatformOrgStructure[]>
  getUserOrgStructureByCostCenterCode(costCenterCode: string): Promise<PlatformOrgStructure[]>
  searchAgencyOrgStructure(searchTerm: string): Promise<Agency[]>
  getWorkFrontData(id: string, entityName: string, appName: string): Promise<WorkFrontData[]>
  getPublicHolidaysCalendar(holidayCalendarCode: string, year: string): Promise<Holiday[]>
  getPlaceholderByWorkCode(workCode: string): Promise<PlaceholderByWorkCode[]>
}

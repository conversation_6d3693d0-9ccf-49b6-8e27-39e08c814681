import { PagingResult } from '../../Domain/Common/PagingResult'
import { Sort } from '../../Domain/Common/Sort'
import { OrgStructureByResourceAssignmentResponse } from '../../Domain/ResourceSlice/OrgStructureByResourceAssignmentResponse'
import { Resource } from '../../Domain/ResourceSlice/Resource'

export interface IResourceRepository {
  getResourceById(resourceId: string): Promise<Resource>
  getResourcesForProjectManager(
    userId: string,
    projectIds: string[],
    pageNumber: number,
    pageSize: number,
    sort: Sort,
    searchName?: string
  ): Promise<PagingResult<Resource>>
  getResourcesForResourceManager(
    resourceManagerId: string,
    order: string,
    pageNumber: number,
    pageSize: number,
    searchName?: string
  ): Promise<PagingResult<Resource>>
  getResourcesByFilters(
    userRole: string,
    resourceManagerId: string,
    projectIds: string[],
    brandIds: string[],
    userIds: string[],
    taskStates: string[],
    taskIds: string[],
    usersGroupIds: number[],
    startDate: string,
    endDate: string,
    isProjectFilterApplied: boolean,
    pageNumber: number,
    pageSize: number,
    sort: Sort
  ): Promise<PagingResult<Resource>>

  getOrgStructureByResourceAssignments(
    userRole: string,
    projectIds: string[],
    userId: string
  ): Promise<OrgStructureByResourceAssignmentResponse>
}

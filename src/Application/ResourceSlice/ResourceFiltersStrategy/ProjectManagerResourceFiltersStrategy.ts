import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Resource } from '../../../Domain/ResourceSlice/Resource'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IResourceRepository } from '../IResourceRepository'
import { IResourceFiltersStrategy } from './IResourceFiltersStrategy'
import { ResourceByFiltersRequest } from '../ResourceByFiltersRequest'
import { USER_ROLE_PROJECT_MANAGER } from '../../../Constants/user_roles'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import ILogger from '../../../Domain/Common/ILogger'

export class ProjectManagerResourceFiltersStrategy implements IResourceFiltersStrategy {
  constructor(
    private resourcesRepository: IResourceRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger // Inject ILogger
  ) {}

  async fetchResources(params: ResourceByFiltersRequest): Promise<PagingResult<Resource>> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userId)
      if (!projects) {
        // No projects found (404 or empty result)
        this.logger.info('No projects found for this project manager (empty result).')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      const projectIds = projects.map((project) => project.projectId)

      // Merge projectIds with params.projectIds if params.projectIds is not null
      const mergedProjectIds = params.projects && params.projects.length > 0 ? params.projects : projectIds
      const isProjectFilterApplied = params.projects && params.projects.length > 0

      // Fetch resources for project manager
      return await this.resourcesRepository.getResourcesByFilters(
        USER_ROLE_PROJECT_MANAGER,
        params.userId,
        mergedProjectIds,
        params.brands,
        params.resources,
        params.taskStates,
        params.tasks,
        params.usersGroups,
        params.startDate,
        params.endDate,
        isProjectFilterApplied,
        params.pageNumber,
        params.pageSize,
        params.sort
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        this.logger.info('No projects found for this project manager (404). Returning empty Resource list.')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      this.logger.error('Error fetching resources for project manager:', error)
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

import { USER_ROLE_RESOURCE_MANAGER } from '../../../Constants/user_roles'
import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Resource } from '../../../Domain/ResourceSlice/Resource'
import { IResourceRepository } from '../IResourceRepository'
import { ResourceByFiltersRequest } from '../ResourceByFiltersRequest'
import { IResourceFiltersStrategy } from './IResourceFiltersStrategy'

export class ResourceManagerResourceFiltersStrategy implements IResourceFiltersStrategy {
  constructor(private resourcesRepository: IResourceRepository) {}

  async fetchResources(params: ResourceByFiltersRequest): Promise<PagingResult<Resource>> {
    // Fetch resources for resource manager
    return await this.resourcesRepository.getResourcesByFilters(
      USER_ROLE_RESOURCE_MANAGER,
      params.userId,
      params.projects,
      params.brands,
      params.resources,
      params.taskStates,
      params.tasks,
      params.usersGroups,
      params.startDate,
      params.endDate,
      params.projects && params.projects.length > 0,
      params.pageNumber,
      params.pageSize,
      params.sort
    )
  }
}

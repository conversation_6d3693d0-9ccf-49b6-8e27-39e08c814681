import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import ILogger from '../../Domain/Common/ILogger'
import { ProjectManagerResourceOrgStructureStrategy } from './ResourceOrgStructureStrategy/ProjectManagerResourceOrgStructureStrategy'
import { ResourceManagerResourceOrgStructureStrategy } from './ResourceOrgStructureStrategy/ResourceManagerResourceOrgStructureStrategy'
import { IResourceOrgStructureStrategy } from './ResourceOrgStructureStrategy/IResourceOrgStructureStrategy'
import { IResourceRepository } from './IResourceRepository'

export class ResourceOrgStructureStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    resourceRepository: IResourceRepository,
    logger: ILogger // Add logger parameter
  ): IResourceOrgStructureStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerResourceOrgStructureStrategy(resourceRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerResourceOrgStructureStrategy(resourceRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

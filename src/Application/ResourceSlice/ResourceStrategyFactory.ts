import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER, USER_ROLE_LIGHT_USER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourceRepository } from './IResourceRepository'
import { IResourceStrategy } from './ResouceUserRolesStrategy/IResourceStrategy'
import { ProjectManagerResourceStrategy } from './ResouceUserRolesStrategy/ProjectManagerResourceStrategy'
import { ResourceManagerResourceStrategy } from './ResouceUserRolesStrategy/ResourceManagerResourceStrategy'
import { LightUserResourceStrategy } from './ResouceUserRolesStrategy/LightUserResourceStrategy'
import ILogger from '../../Domain/Common/ILogger' // Adjust this path as needed based on your project structure

export class ResourceStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    resourcesRepository: IResourceRepository,
    userName: string,
    logger: ILogger // Add logger parameter
  ): IResourceStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerResourceStrategy(resourcesRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerResourceStrategy(resourcesRepository)

      case USER_ROLE_LIGHT_USER:
        return new LightUserResourceStrategy(userName)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

import { Holiday } from '../../Domain/ResourceSlice/Holiday'
import { TimeOffDetails } from './../../Domain/TimesheetsSlice/Timesheets'
export interface ResourceDto {
  id: string
  integrationId: string
  name: string
  location: string
  position: string
  jobTitle: string
  profitCenter: string
  altairNumber: string
  totalCapacity: number
  minimumAgencyHoursPerDay: number
  workCode: string
  agencyName: string
  agencyCode: string
  timeOffDetails: TimeOffDetails[]
  holidays: Holiday[]
  requiresAssignApproval: boolean
}

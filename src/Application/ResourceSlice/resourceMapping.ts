import { DEFAULT_MINIMUM_WORKING_HOURS } from '../../Constants/minimum_working_hours'
import { Holiday } from '../../Domain/ResourceSlice/Holiday'
import { Resource } from '../../Domain/ResourceSlice/Resource'
import { ResourcePlatform } from '../../Domain/ResourceSlice/ResourcePlatform'
import { TimeOffDetails } from '../../Domain/TimesheetsSlice/Timesheets'
import { ResourceDto } from './ResourceDto'

export function mapToResourceDto(
  resource: Resource,
  platformResource: ResourcePlatform | undefined,
  timeOffDetails: TimeOffDetails[] = [],
  holidays: Holiday[] = []
): ResourceDto {
  // Map fields according to the GraphQL schema structure
  return {
    id: resource.externalId, // Assuming the `id` comes from externalId
    integrationId: resource.integrationId,
    name: platformResource?.firstName + ' ' + platformResource?.lastName || resource.name || '',
    location: platformResource?.jobInformation?.officeCity || '',
    position: platformResource?.jobInformation?.childJobFunction || '',
    // Use jobTitle from resource if available, otherwise fallback to platformResource's businessTitle
    jobTitle: resource.jobTitle?.trim() ? resource.jobTitle : platformResource?.jobInformation?.businessTitle || '',
    profitCenter: platformResource?.jobInformation?.businessUnitName || '',
    altairNumber: platformResource?.employeeCode || '',
    totalCapacity: 8,
    minimumAgencyHoursPerDay:
      platformResource?.jobInformation?.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS,
    // Use workCode from resource if available, otherwise fallback to platformResource's jobInformation's workCode
    workCode: resource.workCode?.trim() ? resource.workCode : platformResource?.jobInformation?.workCode || '',
    agencyName: platformResource?.jobInformation?.agencyName || '',
    agencyCode: platformResource?.jobInformation?.agencyCode || '',
    timeOffDetails,
    holidays,
    requiresAssignApproval: resource.requiresAssignApproval || false
  }
}

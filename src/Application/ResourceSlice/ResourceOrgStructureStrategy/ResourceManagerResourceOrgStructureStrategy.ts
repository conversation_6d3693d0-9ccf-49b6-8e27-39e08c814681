import { USER_ROLE_RESOURCE_MANAGER } from '../../../Constants/user_roles'
import { GetOrgStructureByResourceAssignmentsRequest } from '../../../Domain/ResourceSlice/GetOrgStructureByResourceAssignmentsRequest'
import { OrgStructureByResourceAssignmentResponse } from '../../../Domain/ResourceSlice/OrgStructureByResourceAssignmentResponse'
import { IResourceRepository } from '../IResourceRepository'
import { IResourceOrgStructureStrategy } from './IResourceOrgStructureStrategy'

export class ResourceManagerResourceOrgStructureStrategy implements IResourceOrgStructureStrategy {
  constructor(private resourceRepository: IResourceRepository) {}

  async fetchOrgStructure(
    params: GetOrgStructureByResourceAssignmentsRequest
  ): Promise<OrgStructureByResourceAssignmentResponse> {
    // Fetch resources for resource manager
    return await this.resourceRepository.getOrgStructureByResourceAssignments(
      USER_ROLE_RESOURCE_MANAGER,
      null,
      params.userLoggedInExternalId
    )
  }
}

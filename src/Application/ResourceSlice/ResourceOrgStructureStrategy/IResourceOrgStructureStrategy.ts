import { GetOrgStructureByResourceAssignmentsRequest } from '../../../Domain/ResourceSlice/GetOrgStructureByResourceAssignmentsRequest'
import { OrgStructureByResourceAssignmentResponse } from '../../../Domain/ResourceSlice/OrgStructureByResourceAssignmentResponse'

export interface IResourceOrgStructureStrategy {
  fetchOrgStructure(
    params: GetOrgStructureByResourceAssignmentsRequest
  ): Promise<OrgStructureByResourceAssignmentResponse>
}

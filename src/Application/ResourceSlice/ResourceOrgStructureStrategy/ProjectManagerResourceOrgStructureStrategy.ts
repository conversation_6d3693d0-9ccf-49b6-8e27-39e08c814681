import { USER_ROLE_PROJECT_MANAGER } from '../../../Constants/user_roles'
import ILogger from '../../../Domain/Common/ILogger'
import { GetOrgStructureByResourceAssignmentsRequest } from '../../../Domain/ResourceSlice/GetOrgStructureByResourceAssignmentsRequest'
import { OrgStructureByResourceAssignmentResponse } from '../../../Domain/ResourceSlice/OrgStructureByResourceAssignmentResponse'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IResourceRepository } from '../IResourceRepository'
import { IResourceOrgStructureStrategy } from './IResourceOrgStructureStrategy'

export class ProjectManagerResourceOrgStructureStrategy implements IResourceOrgStructureStrategy {
  constructor(
    private resourceRepository: IResourceRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger // Inject ILogger for logging
  ) {}

  async fetchOrgStructure(
    params: GetOrgStructureByResourceAssignmentsRequest
  ): Promise<OrgStructureByResourceAssignmentResponse> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userLoggedInExternalId)

      if (!projects) {
        // No projects found (404 or empty result)
        return {
          agencyCode: [],
          costCenterCode: [],
          location: []
        }
      }

      const projectIds = projects.map((project) => project.projectId)

      // Fetch resources for project manager
      return await this.resourceRepository.getOrgStructureByResourceAssignments(
        USER_ROLE_PROJECT_MANAGER,
        projectIds,
        params.userLoggedInExternalId
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('Returning empty project data.')
        return {
          agencyCode: [],
          costCenterCode: [],
          location: []
        }
      }
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

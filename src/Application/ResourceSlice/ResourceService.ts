import { inject, injectable } from 'tsyringe'
import { IResourceRepository } from './IResourceRepository'
import { ResourceRequest } from './ResourceRequest'
import { ResourceDto } from './ResourceDto'
import { IResourcePlatformRepository } from './IResourcePlatformRepository'
import { mapToResourceDto } from './resourceMapping'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'
import { ResourceStrategyFactory } from './ResourceStrategyFactory'
import { getAgencies } from '../Common/ResourceUtils'
import { AgencyRule } from '../../Domain/ResourceSlice/AgencyRule'
import { ResourceOrgStructure } from '../../Domain/ResourceSlice/ResourceOrgStructure'
import { DEFAULT_MINIMUM_WORKING_HOURS } from '../../Constants/minimum_working_hours'
import { Agency } from '../../Domain/ResourceSlice/Agency'
import { CostCenter } from '../../Domain/ResourceSlice/CostCenter'
import { OrgRequest } from './OrgRequest'
import { ITimesheetsRepository } from '../TimesheetsSlice/TimesheetsRepository'
import { Holiday } from '../../Domain/ResourceSlice/Holiday'
import { HolidayAndTimeOffDto } from './HolidayAndTimeOffDto'
import { HolidayAndTimeOffRequest } from './HolidayAndTimeOffRequest'
import { ResourceByFiltersRequest } from './ResourceByFiltersRequest'
import { ResourceFiltersStrategyFactory } from './ResourceFiltersStrategyFactory'
import { Resource } from '../../Domain/ResourceSlice/Resource'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import ILogger from '../../Domain/Common/ILogger'
import { GetOrgStructureByResourceAssignmentsRequest } from '../../Domain/ResourceSlice/GetOrgStructureByResourceAssignmentsRequest'
import { GetOrgStructureByAssignmentsResponse } from '../../Domain/PlaceholderSlice/GetOrgStructureByAssignmentsResponse'
import { ResourceOrgStructureStrategyFactory } from './ResourceOrgStructureStrategyFactory'
import { prepareAgenciesData, prepareCostCentersData } from '../Common/Utils/PrepareOrgStructureData'

@injectable()
export default class ResourceService {
  constructor(
    @inject('IResourceRepository')
    private resourcesRepository: IResourceRepository,
    @inject('IProjectRepository')
    private projectsRepository: IProjectRepository,
    @inject('IResourcePlatformRepository')
    private resourcesPlatformRepository: IResourcePlatformRepository,
    @inject('ITimesheetsRepository')
    private timesheetsRepository: ITimesheetsRepository,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext,
    @inject('ILogger') private logger: ILogger // Inject ILogger
  ) {}

  async getResourceById(id: string): Promise<ResourceDto> {
    const platformResource = await this.resourcesPlatformRepository.getResourceById(id)
    const mockResource = await this.resourcesRepository.getResourceById(id)

    let agencyInfo = undefined
    if (platformResource.jobInformation.agencyCode) {
      agencyInfo = await this.resourcesPlatformRepository.getResourceAgencyRules(
        platformResource.jobInformation.agencyCode
      )
    }

    platformResource.jobInformation.minimumAgencyHoursPerDay =
      agencyInfo?.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS
    platformResource.jobInformation.agencyName = agencyInfo?.agencyName || 'Agency not found'

    return mapToResourceDto(mockResource, platformResource)
  }

  async getResources(params: ResourceRequest): Promise<PagingResult<ResourceDto>> {
    const userProfile = this.userSessionContext.profile
    const userName = this.userSessionContext.name
    const strategy = ResourceStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.resourcesRepository,
      userName,
      this.logger
    )
    try {
      const rmResources = await strategy.fetchResources(params)

      if (!rmResources || !rmResources.items) {
        // No resources found (404 or empty result)
        this.logger.info('No resources found for this request (empty result).')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }

      return this.processResources(rmResources, params.startDate, params.endDate)
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('No resources found for this request.')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      this.logger.error('Error fetching resources:', error)
      throw error
    }
  }

  async getResourcesByFilters(params: ResourceByFiltersRequest): Promise<PagingResult<ResourceDto>> {
    const userProfile = this.userSessionContext.profile
    const strategy = ResourceFiltersStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.resourcesRepository,
      this.logger
    )
    try {
      const rmResources = await strategy.fetchResources(params)

      if (!rmResources || !rmResources.items) {
        // No resources found (404 or empty result)
        this.logger.info('No resources found for this request (empty result).')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }

      return this.processResources(rmResources, params.startDate, params.endDate)
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('No resources found for this request.')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      this.logger.error('Error fetching resources by filters:', error)
      throw new Error('Invalid resources data. Try again.')
    }
  }

  async getAgenciesData(agencyCodes: string[]) {
    const agenciesData = await this.resourcesPlatformRepository.getAgenciesData(agencyCodes)
    return agenciesData
  }

  async getResourceOrgStructure(id: string): Promise<ResourceOrgStructure> {
    const resource = await this.resourcesPlatformRepository.getResourceById(id)
    if (!resource) {
      throw new Error('Invalid employee data. Try again.')
    }
    const orgStructure = await this.resourcesPlatformRepository.getUserOrgStructure(
      resource.jobInformation.agencyCode,
      resource.jobInformation.costCenterCode
    )
    if (!orgStructure) {
      throw new Error('Invalid orgStructure data. Try again.')
    }

    let costCenterName = ''
    let locationName = ''

    for (const profitCenter of orgStructure[0]?.profitCenters ?? []) {
      const foundCostCenter = profitCenter.costCenters?.find(
        (cc) => cc.costCenterCode === resource.jobInformation.costCenterCode
      )
      if (foundCostCenter) {
        costCenterName = foundCostCenter.costCenterName ?? ''
        locationName = foundCostCenter.costCenterCity ?? ''
        break
      }
    }

    const response: ResourceOrgStructure = {
      agencyName: orgStructure[0]?.agency?.agencyName ?? '',
      locationName,
      costCenterName,
      agencyCode: resource.jobInformation.agencyCode,
      costCenterCode: resource.jobInformation.costCenterCode
    }
    return response
  }

  async getOrgStructureByAgencyCodeCostCenterCode(params: OrgRequest): Promise<ResourceOrgStructure> {
    if (!params.agencyCode) {
      return {
        agencyName: '',
        locationName: '',
        costCenterName: '',
        agencyCode: '',
        costCenterCode: ''
      }
    }

    const orgStructure = await this.resourcesPlatformRepository.getUserOrgStructure(
      params.agencyCode,
      params.costCenterCode
    )

    if (!Array.isArray(orgStructure) || orgStructure.length === 0) {
      throw new Error('Invalid orgStructure data. Try again.')
    }

    // Try to find the cost center in the org structure if costCenterCode is provided
    let costCenterName = ''
    let locationName = ''
    if (params.costCenterCode) {
      for (const profitCenter of orgStructure[0]?.profitCenters ?? []) {
        const foundCostCenter = profitCenter.costCenters?.find((cc) => cc.costCenterCode === params.costCenterCode)
        if (foundCostCenter) {
          costCenterName = foundCostCenter.costCenterName ?? ''
          locationName = foundCostCenter.costCenterCity ?? ''
          break
        }
      }
    }

    return {
      agencyName: orgStructure[0]?.agency?.agencyName ?? '',
      locationName,
      costCenterName,
      agencyCode: params.agencyCode,
      costCenterCode: params.costCenterCode ?? ''
    }
  }

  async getOrgStructureByAgencyCodesCostCenterCodes(params: OrgRequest[]): Promise<ResourceOrgStructure[]> {
    const orgStructures: ResourceOrgStructure[] = []
    for (const param of params) {
      const orgStructure = await this.getOrgStructureByAgencyCodeCostCenterCode(param)
      orgStructures.push(orgStructure)
    }
    return orgStructures
  }

  async searchAgencyOrgStructure(searchTerm: string): Promise<Agency[]> {
    return await this.resourcesPlatformRepository.searchAgencyOrgStructure(searchTerm)
  }

  async getLocationByAgencyOrgStructure(agencyCode: string): Promise<string[]> {
    const orgStructure = await this.resourcesPlatformRepository.getUserOrgStructureByAgencyCode(agencyCode)
    if (!orgStructure) {
      throw new Error('Invalid orgStructure data. Try again.')
    }

    const costCenterCities = orgStructure.flatMap((structure) =>
      structure.profitCenters.flatMap((profitCenter) =>
        profitCenter.costCenters.map((costCenter) => costCenter.costCenterCity)
      )
    )

    return Array.from(new Set(costCenterCities.map((city) => city.toUpperCase()))).sort()
  }

  async getLocationsByAgenciesOrgStructure(agencyCodes: string[]): Promise<string[]> {
    const orgStructures = await Promise.all(
      agencyCodes.map((agencyCode) => this.resourcesPlatformRepository.getUserOrgStructureByAgencyCode(agencyCode))
    )
    if (orgStructures.length === 0 || orgStructures.some((orgStructure) => !orgStructure)) {
      throw new Error('Invalid orgStructure data. Try again.')
    }
    const costCenterCities = orgStructures.flatMap((orgStructureArr) =>
      orgStructureArr.flatMap((orgStructure) =>
        orgStructure.profitCenters.flatMap((profitCenter) =>
          profitCenter.costCenters.map(
            (costCenter) => `${orgStructure.agency.agencyCode}: ${costCenter.costCenterCity}`
          )
        )
      )
    )
    return Array.from(new Set(costCenterCities.map((city) => city.toUpperCase()))).sort()
  }

  async getCostCenterByLocationOrgStructure(agencyCode: string, city: string | null): Promise<CostCenter[]> {
    const orgStructure = await this.resourcesPlatformRepository.getUserOrgStructureByAgencyCode(agencyCode)
    if (!orgStructure) {
      throw new Error('Invalid orgStructure data. Try again.')
    }
    const costCenters = orgStructure.flatMap((structure) =>
      structure.profitCenters.flatMap((profitCenter) =>
        profitCenter.costCenters
          .filter((costCenter) => !city || costCenter.costCenterCity.toLowerCase() === city.toLowerCase())
          .map((costCenter) => ({
            costCenterName: costCenter.costCenterName,
            costCenterCode: costCenter.costCenterCode,
            costCenterCity: costCenter.costCenterCity
          }))
      )
    )

    const uniqueCostCenters = costCenters
      .filter(
        (costCenter, index, self) =>
          index ===
          self.findIndex(
            (c) => c.costCenterCode === costCenter.costCenterCode && c.costCenterName === costCenter.costCenterName
          )
      )
      .sort((a, b) => a.costCenterName.localeCompare(b.costCenterName))

    return uniqueCostCenters
  }

  async getCostCentersByLocationsOrgStructure(agencyCodes: string[], cities: string[] | null): Promise<CostCenter[]> {
    const orgStructures = await Promise.all(
      agencyCodes.map((agencyCode) => this.resourcesPlatformRepository.getUserOrgStructureByAgencyCode(agencyCode))
    )
    if (orgStructures.length === 0 || orgStructures.some((orgStructure) => !orgStructure)) {
      throw new Error('Invalid orgStructure data. Try again.')
    }
    const costCenters = orgStructures.flatMap((orgStructureArr) =>
      orgStructureArr.flatMap((orgStructure) =>
        orgStructure.profitCenters.flatMap((profitCenter) =>
          profitCenter.costCenters
            .filter((costCenter) => {
              if (!cities || cities.length === 0) return true
              return cities.some((city) => {
                const cityAfterColon = city.includes(':')
                  ? city?.split(':')[1]?.trim()?.toLowerCase()
                  : city?.toLowerCase()
                const costCenterCity = costCenter.costCenterCity?.toLowerCase()
                return !city || costCenterCity === cityAfterColon
              })
            })
            .map((costCenter) => ({
              costCenterName: costCenter.costCenterName,
              costCenterCode: costCenter.costCenterCode,
              costCenterCity: costCenter.costCenterCity
            }))
        )
      )
    )

    const uniqueCostCenters = costCenters
      .filter(
        (costCenter, index, self) =>
          index ===
          self.findIndex(
            (c) => c.costCenterCode === costCenter.costCenterCode && c.costCenterName === costCenter.costCenterName
          )
      )
      .sort((a, b) => a.costCenterName.localeCompare(b.costCenterName))

    return uniqueCostCenters
  }

  async getMapHolidayCalendar(holidayCalendarIds: string[], year: string) {
    const holidayCalendarMap: { [key: string]: Holiday[] } = {}
    const uniqueHolidayCalendarIds = Array.from(new Set(holidayCalendarIds)).filter(
      (id) => id !== null && id !== undefined
    )
    // Fetch public holidays for each unique holiday calendar ID and store them in a map where the key is the holidayCalendarId and the value is an array of Holiday objects.

    await Promise.all(
      uniqueHolidayCalendarIds.map(async (holidayCalendarId) => {
        const holidays = await this.resourcesPlatformRepository.getPublicHolidaysCalendar(holidayCalendarId, year)
        holidayCalendarMap[holidayCalendarId] = holidays
      })
    )
    return holidayCalendarMap
  }

  async getHolidaysAndTimeOffDetailsByResourceId(params: HolidayAndTimeOffRequest): Promise<HolidayAndTimeOffDto> {
    const platformResource = await this.resourcesPlatformRepository.getResourceById(params.resourceId)
    if (!platformResource) {
      throw new Error('Invalid employee data. Try again.')
    }
    const timeOffDetails = await this.timesheetsRepository.getTimeOffForResource({
      employeeCode: params.resourceId,
      weekStartDate: params.startDate,
      weekEndDate: params.endDate
    })
    const year = new Date(params.endDate).getFullYear().toString()
    const holidays = await this.resourcesPlatformRepository.getPublicHolidaysCalendar(
      platformResource.jobInformation.holidayCalendarId,
      year
    )
    const response: HolidayAndTimeOffDto = {
      holidays,
      timeOffDetails
    }
    return response
  }

  async getOrgStructureByAgencyCode(agencyCode: string): Promise<AgencyRule> {
    const agencyRule = await this.resourcesPlatformRepository.getResourceAgencyRules(agencyCode)
    if (!agencyRule) {
      throw new Error(`Agency not found for agencyCode: ${agencyCode}`)
    }
    return {
      minimumAgencyHoursPerDay: agencyRule.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS,
      agencyName: agencyRule.agencyName ?? 'Agency not found',
      agencyCode: agencyRule.agencyCode ?? 'Agency not found'
    }
  }

  async getOrgStructureByResourceAssignments(
    params: GetOrgStructureByResourceAssignmentsRequest
  ): Promise<GetOrgStructureByAssignmentsResponse> {
    const userProfile = this.userSessionContext.profile
    const strategy = ResourceOrgStructureStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.resourcesRepository,
      this.logger
    )
    const orgStructure = await strategy.fetchOrgStructure(params)

    if (!orgStructure || !orgStructure.agencyCode || orgStructure.agencyCode.length === 0) {
      return {
        agencies: [],
        costCenters: [],
        locations: []
      }
    }

    // Filter out null, undefined, empty, and '' values from agencyCode
    const filteredAgencyCodes = Array.from(
      new Set(orgStructure.agencyCode.filter((code) => code && code.trim() !== ''))
    )

    // Filter out null, undefined, empty, and '' values from costCenterCode
    const filteredCostCenterCodes = Array.from(
      new Set(orgStructure.costCenterCode.filter((code) => code && code.trim() !== ''))
    )

    // Call the methods with filtered and unique values
    const agencies = await prepareAgenciesData(this.resourcesPlatformRepository, filteredAgencyCodes)
    const costcenters = await prepareCostCentersData(this.resourcesPlatformRepository, filteredCostCenterCodes)
    const locations = Array.from(new Set(orgStructure.location.filter((code) => code && code.trim() !== ''))).sort()

    const orderedCostCenters = [
      ...costcenters,
      { costCenterName: '<Blank>', costCenterCode: '<Blank>', costCenterCity: '<Blank>' }
    ]
    const orderedLocations = [...locations, '<Blank>']

    return {
      agencies: agencies,
      costCenters: orderedCostCenters,
      locations: orderedLocations
    }
  }

  private async processResources(
    rmResources: PagingResult<Resource>,
    startDate: string,
    endDate: string
  ): Promise<PagingResult<ResourceDto>> {
    // Extract external IDs (altair codes) from resources
    const externalIds = rmResources.items.map((resource) => resource.externalId)
    // Fetch platform resources using external IDs
    const platformResources = await this.resourcesPlatformRepository.getResourcesByWorkCodes(externalIds)
    const holidayCalendarIds = platformResources.map((resource) => resource.jobInformation.holidayCalendarId)
    // Fetch holiday calendar data
    const year = new Date(endDate).getFullYear().toString()
    const holidayCalendarMap = await this.getMapHolidayCalendar(holidayCalendarIds, year)

    const agenciesData: { [key: string]: AgencyRule } = {}
    // Collect all unique agency codes, avoiding nulls
    const agencyCodes = Array.from(
      new Set(
        platformResources
          .map((resource) => {
            return resource.jobInformation?.agencyCode
          })
          .filter((code): code is string => code !== undefined)
      )
    )

    // Fetch agency data
    await getAgencies(agencyCodes, agenciesData, this.resourcesPlatformRepository)

    const mergedResources = await Promise.all(
      rmResources.items.map(async (resource) => {
        let holidays = []
        const platformResource = platformResources.find((p) => p.employeeCode === resource.externalId)

        if (platformResource) {
          holidays = holidayCalendarMap[platformResource.jobInformation?.holidayCalendarId] || []

          const agencyCode = platformResource?.jobInformation?.agencyCode
          if (agencyCode) {
            const agencyData = agenciesData[agencyCode]
            platformResource.jobInformation.minimumAgencyHoursPerDay = agencyData.minimumAgencyHoursPerDay
            platformResource.jobInformation.agencyName = agencyData.agencyName
            platformResource.jobInformation.agencyCode = agencyCode
          }
        }
        const timeOffDetails = await this.timesheetsRepository.getTimeOffForResource({
          employeeCode: resource.externalId,
          weekStartDate: startDate,
          weekEndDate: endDate
        })
        return mapToResourceDto(resource, platformResource, timeOffDetails, holidays)
      })
    )

    const rmResourcesPaginated: PagingResult<ResourceDto> = {
      hasNextPage: rmResources.hasNextPage,
      hasPreviousPage: rmResources.hasPreviousPage,
      pageNumber: rmResources.pageNumber,
      totalCount: rmResources.totalCount,
      totalPages: rmResources.totalPages,
      items: mergedResources
    }

    return rmResourcesPaginated
  }
}

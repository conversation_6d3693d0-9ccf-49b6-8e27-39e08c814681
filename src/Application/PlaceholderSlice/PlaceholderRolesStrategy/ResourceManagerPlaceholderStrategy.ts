import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Placeholder } from '../../../Domain/PlaceholderSlice/Placeholder'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { IPlaceholderStrategy } from './IPlaceholderStrategy'
import { PlaceholderRequest } from '../PlaceholderRequest'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'
import { prepareOrgStructureData } from '../../Common/Utils/PrepareOrgStructureData'

export class ResourceManagerPlaceholderStrategy implements IPlaceholderStrategy {
  constructor(
    private placeholderRepository: IPlaceholderRepository,
    private resourcesPlatformRepository: IResourcePlatformRepository
  ) {}

  async fetchPlaceholders(params: PlaceholderRequest): Promise<PagingResult<Placeholder>> {
    const { agencyCodes, costCenterCodes, costCenterCities } = await prepareOrgStructureData(
      this.resourcesPlatformRepository,
      params.userId
    )

    // Fetch placeholders for resource manager
    return await this.placeholderRepository.getPlaceholdersForResourceManager(
      params.userId,
      params.sort.order,
      agencyCodes,
      costCenterCodes,
      costCenterCities,
      params.pageNumber,
      params.pageSize,
      params.isExpandedPlaceholderApplied
    )
  }
}

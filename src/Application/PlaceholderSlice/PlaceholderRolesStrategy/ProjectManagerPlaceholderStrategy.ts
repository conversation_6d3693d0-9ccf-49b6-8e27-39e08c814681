import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Placeholder } from '../../../Domain/PlaceholderSlice/Placeholder'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { IPlaceholderStrategy } from './IPlaceholderStrategy'
import { PlaceholderRequest } from '../PlaceholderRequest'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import ILogger from '../../../Domain/Common/ILogger'
export class ProjectManagerPlaceholderStrategy implements IPlaceholderStrategy {
  constructor(
    private resourcesRepository: IPlaceholderRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger
    // Default to console if not provided
  ) {}

  async fetchPlaceholders(params: PlaceholderRequest): Promise<PagingResult<Placeholder>> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userId)
      if (!projects) {
        // No projects found (404 or empty result)
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }

      const projectIds = projects.map((project) => project.projectId)

      // Fetch placeholders for project manager
      return await this.resourcesRepository.getPlaceholdersForProjectManager(
        params.userId,
        projectIds,
        params.pageNumber,
        params.pageSize,
        params.sort
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('Returning empty project data.')

        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

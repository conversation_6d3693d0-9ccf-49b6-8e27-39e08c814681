import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderRepository } from './IPlaceholderRepository'
import { IPlaceholderStrategy } from './PlaceholderRolesStrategy/IPlaceholderStrategy'
import { ProjectManagerPlaceholderStrategy } from './PlaceholderRolesStrategy/ProjectManagerPlaceholderStrategy'
import { ResourceManagerPlaceholderStrategy } from './PlaceholderRolesStrategy/ResourceManagerPlaceholderStrategy'
import ILogger from '../../Domain/Common/ILogger'
export class PlaceholderStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    placeholderRepository: IPlaceholderRepository,
    resourcesPlatformRepository: IResourcePlatformRepository,
    logger: ILogger
  ): IPlaceholderStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerPlaceholderStrategy(placeholderRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerPlaceholderStrategy(placeholderRepository, resourcesPlatformRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Placeholder } from '../../../Domain/PlaceholderSlice/Placeholder'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderFiltersStrategy } from './IPlaceholderFiltersStrategy'
import { PlaceholderByFiltersRequest } from '../PlaceholderByFiltersRequest'
import { USER_ROLE_RESOURCE_MANAGER } from '../../../Constants/user_roles'
import { prepareOrgStructureData } from '../../Common/Utils/PrepareOrgStructureData'

export class ResourceManagerPlaceholderFiltersStrategy implements IPlaceholderFiltersStrategy {
  constructor(
    private placeholderRepository: IPlaceholderRepository,
    private resourcesPlatformRepository: IResourcePlatformRepository
  ) {}

  async fetchPlaceholders(params: PlaceholderByFiltersRequest): Promise<PagingResult<Placeholder>> {
    const { agencyCodes, costCenterCodes, costCenterCities } = await prepareOrgStructureData(
      this.resourcesPlatformRepository,
      params.userId
    )

    // Fetch placeholders for resource manager
    return await this.placeholderRepository.getPlaceholdersByFilters(
      USER_ROLE_RESOURCE_MANAGER,
      params.projects,
      params.brands,
      params.placeholders,
      params.taskStates,
      params.tasks,
      params.usersGroups,
      agencyCodes,
      costCenterCodes,
      costCenterCities,
      params.startDate,
      params.endDate,
      params.projects && params.projects.length > 0,
      params.pageNumber,
      params.pageSize,
      params.sort,
      params.userId,
      params.isExpandedPlaceholderApplied
    )
  }
}

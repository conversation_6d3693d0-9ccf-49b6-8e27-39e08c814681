import { USER_ROLE_PROJECT_MANAGER } from '../../../Constants/user_roles'
import { PagingResult } from '../../../Domain/Common/PagingResult'
import { Placeholder } from '../../../Domain/PlaceholderSlice/Placeholder'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { PlaceholderByFiltersRequest } from '../PlaceholderByFiltersRequest'
import { IPlaceholderFiltersStrategy } from './IPlaceholderFiltersStrategy'
import ILogger from '../../../Domain/Common/ILogger'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

export class ProjectManagerPlaceholderFiltersStrategy implements IPlaceholderFiltersStrategy {
  constructor(
    private resourcesRepository: IPlaceholderRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger // Inject ILogger for logging
  ) {}

  async fetchPlaceholders(params: PlaceholderByFiltersRequest): Promise<PagingResult<Placeholder>> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userId)

      if (!projects) {
        // No projects found (404 or empty result)
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }

      const projectIds = projects.map((project) => project.projectId)

      // Merge projectIds with params.projectIds if params.projectIds is not null
      const mergedProjectIds = params.projects && params.projects.length > 0 ? params.projects : projectIds
      const isProjectFilterApplied = params.projects && params.projects.length > 0

      // Fetch placeholders for project manager
      return await this.resourcesRepository.getPlaceholdersByFilters(
        USER_ROLE_PROJECT_MANAGER,
        mergedProjectIds,
        params.brands,
        params.placeholders,
        params.taskStates,
        params.tasks,
        params.usersGroups,
        null,
        null,
        null,
        params.startDate,
        params.endDate,
        isProjectFilterApplied,
        params.pageNumber,
        params.pageSize,
        params.sort,
        params.userId,
        params.isExpandedPlaceholderApplied
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('Returning empty project data.')
        return {
          hasNextPage: false,
          hasPreviousPage: false,
          pageNumber: 0,
          totalCount: 0,
          totalPages: 0,
          items: []
        }
      }
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

import { USER_ROLE_PROJECT_MANAGER } from '../../../Constants/user_roles'
import ILogger from '../../../Domain/Common/ILogger'
import { GetOrgStructureByPlaceholderAssignmentsRequest } from '../../../Domain/PlaceholderSlice/GetOrgStructureByPlaceholderAssignmentsRequest'
import { OrgStructureByPlaceholderAssignmentResponse } from '../../../Domain/PlaceholderSlice/OrgStructureByPlaceholderAssignmentResponse'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { IPlaceholderOrgStructureStrategy } from './IPlaceholderOrgStructureStrategy'

export class ProjectManagerPlaceholderOrgStructureStrategy implements IPlaceholderOrgStructureStrategy {
  constructor(
    private placeholderRepository: IPlaceholderRepository,
    private projectsRepository: IProjectRepository,
    private logger: ILogger // Inject ILogger for logging
  ) {}

  async fetchOrgStructure(
    params: GetOrgStructureByPlaceholderAssignmentsRequest
  ): Promise<OrgStructureByPlaceholderAssignmentResponse> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(params.userLoggedInExternalId)

      if (!projects) {
        // No projects found (404 or empty result)
        return {
          agencyCode: [],
          costCenterCode: [],
          location: []
        }
      }

      const projectIds = projects.map((project) => project.projectId)

      // Fetch placeholders for project manager
      return await this.placeholderRepository.getOrgStructureByPlaceholderAssignments(
        USER_ROLE_PROJECT_MANAGER,
        projectIds,
        null,
        null,
        null,
        params.userLoggedInExternalId
      )
    } catch (error) {
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('Returning empty project data.')
        return {
          agencyCode: [],
          costCenterCode: [],
          location: []
        }
      }
      throw new Error('Invalid projects data. Try again.')
    }
  }
}

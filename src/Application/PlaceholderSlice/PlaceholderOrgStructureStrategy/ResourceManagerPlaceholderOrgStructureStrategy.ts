import { USER_ROLE_RESOURCE_MANAGER } from '../../../Constants/user_roles'
import { GetOrgStructureByPlaceholderAssignmentsRequest } from '../../../Domain/PlaceholderSlice/GetOrgStructureByPlaceholderAssignmentsRequest'
import { OrgStructureByPlaceholderAssignmentResponse } from '../../../Domain/PlaceholderSlice/OrgStructureByPlaceholderAssignmentResponse'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderRepository } from '../IPlaceholderRepository'
import { IPlaceholderOrgStructureStrategy } from './IPlaceholderOrgStructureStrategy'
import { prepareOrgStructureData } from '../../Common/Utils/PrepareOrgStructureData'

export class ResourceManagerPlaceholderOrgStructureStrategy implements IPlaceholderOrgStructureStrategy {
  constructor(
    private placeholderRepository: IPlaceholderRepository,
    private resourcesPlatformRepository: IResourcePlatformRepository
  ) {}

  async fetchOrgStructure(
    params: GetOrgStructureByPlaceholderAssignmentsRequest
  ): Promise<OrgStructureByPlaceholderAssignmentResponse> {
    const { agencyCodes, costCenterCodes, costCenterCities } = await prepareOrgStructureData(
      this.resourcesPlatformRepository,
      params.userLoggedInExternalId
    )

    // Fetch placeholders for resource manager
    return await this.placeholderRepository.getOrgStructureByPlaceholderAssignments(
      USER_ROLE_RESOURCE_MANAGER,
      null,
      agencyCodes,
      costCenterCodes,
      costCenterCities,
      params.userLoggedInExternalId
    )
  }
}

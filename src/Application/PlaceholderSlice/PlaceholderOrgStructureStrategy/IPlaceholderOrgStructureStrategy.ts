import { GetOrgStructureByPlaceholderAssignmentsRequest } from '../../../Domain/PlaceholderSlice/GetOrgStructureByPlaceholderAssignmentsRequest'
import { OrgStructureByPlaceholderAssignmentResponse } from '../../../Domain/PlaceholderSlice/OrgStructureByPlaceholderAssignmentResponse'

export interface IPlaceholderOrgStructureStrategy {
  fetchOrgStructure(
    params: GetOrgStructureByPlaceholderAssignmentsRequest
  ): Promise<OrgStructureByPlaceholderAssignmentResponse>
}

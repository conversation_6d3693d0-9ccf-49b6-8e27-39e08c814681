import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderRepository } from './IPlaceholderRepository'
import { IPlaceholderFiltersStrategy } from './PlaceholderFiltersStrategy/IPlaceholderFiltersStrategy'
import { ProjectManagerPlaceholderFiltersStrategy } from './PlaceholderFiltersStrategy/ProjectManagerPlaceholderFiltersStrategy'
import { ResourceManagerPlaceholderFiltersStrategy } from './PlaceholderFiltersStrategy/ResourceManagerPlaceholderFiltersStrategy'
import ILogger from '../../Domain/Common/ILogger'

export class PlaceholderFiltersStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    placeholderRepository: IPlaceholderRepository,
    resourcesPlatformRepository: IResourcePlatformRepository,
    logger: ILogger // Add logger parameter
  ): IPlaceholderFiltersStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerPlaceholderFiltersStrategy(placeholderRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerPlaceholderFiltersStrategy(placeholderRepository, resourcesPlatformRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

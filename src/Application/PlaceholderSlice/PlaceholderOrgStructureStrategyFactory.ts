import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderRepository } from './IPlaceholderRepository'
import ILogger from '../../Domain/Common/ILogger'
import { IPlaceholderOrgStructureStrategy } from './PlaceholderOrgStructureStrategy/IPlaceholderOrgStructureStrategy'
import { ProjectManagerPlaceholderOrgStructureStrategy } from './PlaceholderOrgStructureStrategy/ProjectManagerPlaceholderOrgStructureStrategy'
import { ResourceManagerPlaceholderOrgStructureStrategy } from './PlaceholderOrgStructureStrategy/ResourceManagerPlaceholderOrgStructureStrategy'

export class PlaceholderOrgStructureStrategyFactory {
  static getStrategy(
    userProfile: string,
    projectRepository: IProjectRepository,
    placeholderRepository: IPlaceholderRepository,
    resourcesPlatformRepository: IResourcePlatformRepository,
    logger: ILogger // Add logger parameter
  ): IPlaceholderOrgStructureStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerPlaceholderOrgStructureStrategy(placeholderRepository, projectRepository, logger)

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerPlaceholderOrgStructureStrategy(placeholderRepository, resourcesPlatformRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

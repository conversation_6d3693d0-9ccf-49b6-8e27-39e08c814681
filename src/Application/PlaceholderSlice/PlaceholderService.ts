import { inject, injectable } from 'tsyringe'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { GetPlaceholderFilterValue } from '../../Domain/PlaceholderSlice/GetPlaceholderFilterValue'
import { Placeholder } from '../../Domain/PlaceholderSlice/Placeholder'
import { PlaceholderFilterValues } from '../../Domain/PlaceholderSlice/PlaceholderFilterValues'
import { AgencyRule } from '../../Domain/ResourceSlice/AgencyRule'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'
import { getAgencies } from '../Common/ResourceUtils'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { IPlaceholderRepository } from './IPlaceholderRepository'
import { PlaceholderByFiltersRequest } from './PlaceholderByFiltersRequest'
import { PlaceholderDto } from './PlaceholderDto'
import { PlaceholderFiltersStrategyFactory } from './PlaceholderFiltersStrategyFactory'
import { mapToPlaceholderDto } from './PlaceholderMapping'
import { PlaceholderRequest } from './PlaceholderRequest'
import { PlaceholderStrategyFactory } from './PlaceholderStrategyFactory'
import { CreateEditPlaceholderFilterValuesRequest } from '../../Domain/PlaceholderSlice/CreateEditPlaceholderFilterValuesRequest'
import ILogger from '../../Domain/Common/ILogger'
import { GetOrgStructureByPlaceholderAssignmentsRequest } from '../../Domain/PlaceholderSlice/GetOrgStructureByPlaceholderAssignmentsRequest'
import { PlaceholderOrgStructureStrategyFactory } from './PlaceholderOrgStructureStrategyFactory'
import { GetOrgStructureByAssignmentsResponse } from '../../Domain/PlaceholderSlice/GetOrgStructureByAssignmentsResponse'
import { prepareAgenciesData, prepareCostCentersData } from '../Common/Utils/PrepareOrgStructureData'
@injectable()
export default class PlaceholderService {
  constructor(
    @inject('IPlaceholderRepository')
    private placeholderRepository: IPlaceholderRepository,
    @inject('IProjectRepository')
    private projectsRepository: IProjectRepository,
    @inject('IResourcePlatformRepository')
    private resourcesPlatformRepository: IResourcePlatformRepository,
    @inject('ILogger')
    private logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {}

  async getPlaceholders(params: PlaceholderRequest): Promise<PagingResult<PlaceholderDto>> {
    const userProfile = this.userSessionContext.profile
    const strategy = PlaceholderStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.placeholderRepository,
      this.resourcesPlatformRepository,
      this.logger
    )
    const rmPlaceholders = await strategy.fetchPlaceholders(params)

    if (!rmPlaceholders || !rmPlaceholders.items) {
      throw new Error('Invalid placeholders data. Try again.')
    }

    return this.processPlaceholders(rmPlaceholders)
  }

  async getPlaceholdersByFilters(params: PlaceholderByFiltersRequest): Promise<PagingResult<PlaceholderDto>> {
    const userProfile = this.userSessionContext.profile
    const strategy = PlaceholderFiltersStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.placeholderRepository,
      this.resourcesPlatformRepository,
      this.logger
    )
    const rmPlaceholders = await strategy.fetchPlaceholders(params)

    if (!rmPlaceholders || !rmPlaceholders.items) {
      throw new Error('Invalid placeholders data. Try again.')
    }

    return this.processPlaceholders(rmPlaceholders)
  }

  private async processPlaceholders(rmPlaceholders: PagingResult<Placeholder>): Promise<PagingResult<PlaceholderDto>> {
    // Extract external IDs (altair codes) from resources
    const externalIds = rmPlaceholders.items.map((resource) => resource.externalId)
    // Fetch platform resources using external IDs
    const platformResources = await this.resourcesPlatformRepository.getResourcesByWorkCodes(externalIds)
    const agenciesData: { [key: string]: AgencyRule } = {}
    // Collect all unique agency codes, avoiding nulls
    const agencyCodes = Array.from(
      new Set(
        platformResources
          .map((resource) => {
            return resource.jobInformation?.agencyCode
          })
          .filter((code): code is string => code !== undefined)
      )
    )

    // Fetch agency data
    await getAgencies(agencyCodes, agenciesData, this.resourcesPlatformRepository)

    // Merge both datasets into PlaceholderDto
    const mergedPlaceholders = await Promise.all(
      rmPlaceholders.items.map(async (placeholder) => {
        const platformResource = platformResources.find((p) => p.employeeCode === placeholder.externalId)
        if (platformResource) {
          const agencyCode = platformResource?.jobInformation?.agencyCode
          if (agencyCode) {
            const agencyData = agenciesData[agencyCode]
            platformResource.jobInformation.minimumAgencyHoursPerDay = agencyData.minimumAgencyHoursPerDay
          }
        }
        return mapToPlaceholderDto(placeholder, platformResource)
      })
    )
    const rmResourcesPaginated: PagingResult<PlaceholderDto> = {
      hasNextPage: rmPlaceholders.hasNextPage,
      hasPreviousPage: rmPlaceholders.hasPreviousPage,
      pageNumber: rmPlaceholders.pageNumber,
      totalCount: rmPlaceholders.totalCount,
      totalPages: rmPlaceholders.totalPages,
      items: mergedPlaceholders
    }

    return rmResourcesPaginated
  }

  async getPlaceholderFilterValuesByUserId(value: GetPlaceholderFilterValue) {
    const placeholderFilters: PlaceholderFilterValues =
      await this.placeholderRepository.getPlaceholderFilterValuesByUserId(value)

    return placeholderFilters
  }

  async createEditPlaceholderFilterValues(params: CreateEditPlaceholderFilterValuesRequest): Promise<boolean> {
    const values = (await this.placeholderRepository.createEditPlaceholderFilterValues(params)) as boolean

    return values
  }

  async getOrgStructureByPlaceholderAssignments(
    params: GetOrgStructureByPlaceholderAssignmentsRequest
  ): Promise<GetOrgStructureByAssignmentsResponse> {
    const userProfile = this.userSessionContext.profile
    const strategy = PlaceholderOrgStructureStrategyFactory.getStrategy(
      userProfile,
      this.projectsRepository,
      this.placeholderRepository,
      this.resourcesPlatformRepository,
      this.logger
    )
    const orgStructure = await strategy.fetchOrgStructure(params)

    if (!orgStructure || !orgStructure.agencyCode || orgStructure.agencyCode.length === 0) {
      return {
        agencies: [],
        costCenters: [],
        locations: []
      }
    }

    // Filter out null, undefined, empty, and '' values from agencyCode
    const filteredAgencyCodes = Array.from(
      new Set(orgStructure.agencyCode.filter((code) => code && code.trim() !== ''))
    )

    // Filter out null, undefined, empty, and '' values from costCenterCode
    const filteredCostCenterCodes = Array.from(
      new Set(orgStructure.costCenterCode.filter((code) => code && code.trim() !== ''))
    )

    // Call the methods with filtered and unique values
    const agencies = await prepareAgenciesData(this.resourcesPlatformRepository, filteredAgencyCodes)
    const costcenters = await prepareCostCentersData(this.resourcesPlatformRepository, filteredCostCenterCodes)
    const locations = Array.from(new Set(orgStructure.location.filter((code) => code && code.trim() !== ''))).sort()

    const orderedCostCenters = [
      ...costcenters,
      { costCenterName: '<Blank>', costCenterCode: '<Blank>', costCenterCity: '<Blank>' }
    ]
    const orderedLocations = [...locations, '<Blank>']

    return {
      agencies: agencies,
      costCenters: orderedCostCenters,
      locations: orderedLocations
    }
  }
}

import { PagingResult } from '../../Domain/Common/PagingResult'
import { Sort } from '../../Domain/Common/Sort'
import { CreateEditPlaceholderFilterValuesRequest } from '../../Domain/PlaceholderSlice/CreateEditPlaceholderFilterValuesRequest'
import { GetPlaceholderFilterValue } from '../../Domain/PlaceholderSlice/GetPlaceholderFilterValue'
import { OrgStructureByPlaceholderAssignmentResponse } from '../../Domain/PlaceholderSlice/OrgStructureByPlaceholderAssignmentResponse'
import { Placeholder } from '../../Domain/PlaceholderSlice/Placeholder'
import { PlaceholderFilterValues } from '../../Domain/PlaceholderSlice/PlaceholderFilterValues'

export interface IPlaceholderRepository {
  getPlaceholdersForProjectManager(
    userId: string,
    projectIds: string[],
    pageNumber: number,
    pageSize: number,
    sort: Sort
  ): Promise<PagingResult<Placeholder>>
  getPlaceholdersForResourceManager(
    resourceManagerId: string,
    order: string,
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    pageNumber: number,
    pageSize: number,
    isExpandedPlaceholderApplied: boolean
  ): Promise<PagingResult<Placeholder>>
  getPlaceholdersByFilters(
    userRole: string,
    projectIds: string[],
    brandIds: string[],
    userIds: string[],
    taskStates: string[],
    taskIds: string[],
    usersGroupIds: number[],
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    startDate: string,
    endDate: string,
    isProjectFilterApplied: boolean,
    pageNumber: number,
    pageSize: number,
    sort: Sort,
    userId: string,
    isExpandedPlaceholderApplied: boolean
  ): Promise<PagingResult<Placeholder>>

  getPlaceholderFilterValuesByUserId(params: GetPlaceholderFilterValue): Promise<PlaceholderFilterValues>

  createEditPlaceholderFilterValues(params: CreateEditPlaceholderFilterValuesRequest): Promise<boolean>

  getOrgStructureByPlaceholderAssignments(
    userRole: string,
    projectIds: string[],
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    userId: string
  ): Promise<OrgStructureByPlaceholderAssignmentResponse>
}

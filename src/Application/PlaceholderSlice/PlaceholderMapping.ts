import { DEFAULT_MINIMUM_WORKING_HOURS } from '../../Constants/minimum_working_hours'
import { Placeholder } from '../../Domain/PlaceholderSlice/Placeholder'
import { ResourcePlatform } from '../../Domain/ResourceSlice/ResourcePlatform'
import { PlaceholderDto } from './PlaceholderDto'

export function mapToPlaceholderDto(
  placeholder: Placeholder,
  platformResource: ResourcePlatform | undefined
): PlaceholderDto {
  // Map fields according to the GraphQL schema structure
  return {
    id: placeholder.externalId, // Assuming the `id` comes from externalId
    name: placeholder.name,
    minimumAgencyHoursPerDay:
      platformResource?.jobInformation?.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS
  }
}

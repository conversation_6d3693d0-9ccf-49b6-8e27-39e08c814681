import { inject, injectable } from 'tsyringe'
import { IProjectRepository } from './IProjectRepository'
import { Project } from '../../Domain/ProjectSlice/Project'
import { orderBy } from 'lodash'
import { IProjectRMRepository } from './IProjectRMRepository'
import { ProjectRM } from '../../Domain/ProjectSlice/ProjectRM'
import { ProjectPM } from '../../Domain/ProjectSlice/ProjectPM'

@injectable()
export default class ProjectService {
  constructor(
    @inject('IProjectRepository')
    private projectRepository: IProjectRepository,
    @inject('IProjectRMRepository')
    private projectRMRepository: IProjectRMRepository
  ) {}

  async getProjectsByProjectManagerWorkCode(workCode: string): Promise<ProjectPM[]> {
    const projects = await this.projectRepository.getProjectsByProjectManagerWorkCode(workCode)
    if (!projects) {
      return []
    }
    const mappedProjects = projects.map(this.mapProject)
    return orderBy(mappedProjects, ['name'], ['asc'])
  }

  async getProjectsByProjectId(projectId: string): Promise<Project> {
    return await this.projectRepository.getProjectsByProjectId(projectId)
  }

  async getProjectsByResourceManagerId(workCode: string, isExpandedPlaceholderApplied: boolean): Promise<ProjectRM[]> {
    return this.projectRMRepository.getProjectsByResourceManagerId(workCode, isExpandedPlaceholderApplied)
  }

  private mapProject(project: Project): ProjectPM {
    return {
      ...project,
      integrationId: project.workfrontId
    }
  }
}

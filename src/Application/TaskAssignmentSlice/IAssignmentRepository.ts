import { ApproveRejectAssignment } from './../../Domain/TaskAssignmentSlice/ApproveRejectAssignment'
import { Assignment } from '../../Domain/TaskAssignmentSlice/Assignment'
import { UpdateAssignmentRequest } from '../../Domain/TaskAssignmentSlice/UpdateAssignmentRequest'
import { AssignmentRequest } from './AssignmentRequest'
import { CreateAssignmentRequest } from './CreateAssignmentRequest'

export interface IAssignmentRepository {
  getAssignments(params: AssignmentRequest): Promise<Assignment[]>
  updateAssignment(assignment: UpdateAssignmentRequest): Promise<boolean>
  getAssignmentById(assignmentId: string): Promise<Assignment>
  createAssignment(params: CreateAssignmentRequest): Promise<Assignment>
  deleteAssignmentById(assignmentId: string, userLoggedInExternalId: string): Promise<boolean>
  approveRejectAssignment(params: ApproveRejectAssignment): Promise<boolean>
}

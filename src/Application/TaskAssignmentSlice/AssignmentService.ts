import { inject, injectable } from 'tsyringe'
import { IAssignmentRepository } from './IAssignmentRepository'
import { AssignmentRequest } from './AssignmentRequest'
import { calculateDaysDifference, calculateDaysDifferenceCoordinates } from '../Common/dateOperators'
import { AssignmentDto } from './AssignmentDto'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'
import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { Project } from '../../Domain/ProjectSlice/Project'
import { CreateAssignmentRequestMutation } from './CreateAssignmentRequest'
import { UpdateAssignmentRequest } from '../../Domain/TaskAssignmentSlice/UpdateAssignmentRequest'
import { ApproveRejectAssignment } from '../../Domain/TaskAssignmentSlice/ApproveRejectAssignment'
import { AgencyRule } from '../../Domain/ResourceSlice/AgencyRule'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { getAgencies, getOrgStructureData } from '../Common/ResourceUtils'
import ResourceService from '../ResourceSlice/ResourceService'
import { prepareOrgStructureData } from '../Common/Utils/PrepareOrgStructureData'

@injectable()
export default class AssignmentService {
  constructor(
    @inject('IAssignmentRepository')
    private assignmentRepository: IAssignmentRepository,
    @inject('IProjectRepository')
    private projectsRepository: IProjectRepository,
    @inject('IResourcePlatformRepository')
    private resourcesPlatformRepository: IResourcePlatformRepository,
    @inject(UserSessionContext)
    protected userSessionContext: UserSessionContext,
    @inject(ResourceService)
    private resourceService: ResourceService
    // Inject the repository interface
  ) {}
  async getAssignments(params: AssignmentRequest): Promise<AssignmentDto[]> {
    const { endDate, startDate, workCode } = params
    const projects = await this.fetchProjectsIfNeeded(workCode)

    if (
      (!params.agencyCode || params.agencyCode.length === 0) &&
      (!params.costCenterCode || params.costCenterCode.length === 0) &&
      (!params.location || params.location.length === 0)
    ) {
      // default values for agencyCodes, costCenterCodes, and costCenterCities
      const { agencyCodes, costCenterCodes, costCenterCities } = await prepareOrgStructureData(
        this.resourcesPlatformRepository,
        params.userLoggedInExternalId
      )

      if (this.userSessionContext.profile === USER_ROLE_RESOURCE_MANAGER) {
        params.agencyCode = agencyCodes
        params.costCenterCode = costCenterCodes
        params.location = costCenterCities
        params.userRole = USER_ROLE_RESOURCE_MANAGER
      }
    }

    const assignments: AssignmentDto[] = await this.assignmentRepository.getAssignments(params)

    // Return an empty array if assignments are null or empty
    if (!assignments || assignments.length === 0) {
      return []
    }
    const agenciesData: { [key: string]: AgencyRule } = {}
    const allProjects = await this.getAllProjects(assignments, projects)
    // Collect all unique agency codes, avoiding nulls
    const projectAgencyCodes = Array.from(
      new Set(
        assignments
          .map((item) => {
            const project = allProjects.find((proj) => proj.projectId === item.projectId)
            return project?.agencyCode
          })
          .filter((code): code is string => code !== undefined)
      )
    )
    // Fetch agency data
    await getAgencies(projectAgencyCodes, agenciesData, this.resourcesPlatformRepository)

    // OrgStructure data for placeholder assignments
    const costCenterCodesAgencyCodes = assignments
      .filter((x) => x.isPlaceholder)
      .reduce(
        (acc, { costCenterCode, agencyCode }) => {
          if (!costCenterCode && agencyCode) {
            const key = `${agencyCode}`
            if (!acc[key]) {
              acc[key] = { costCenterCode: '', agencyCode, costCenterName: '', agencyName: '' }
            }
          } else if (costCenterCode && agencyCode) {
            const key = `${agencyCode}-${costCenterCode}`
            if (!acc[key]) {
              acc[key] = { costCenterCode, agencyCode, costCenterName: '', agencyName: '' }
            }
          }
          return acc
        },
        {} as {
          [key: string]: { costCenterCode: string; agencyCode: string; costCenterName: string; agencyName: string }
        }
      )

    // Directly update costCenterCodesAgencyCodes using the improved getOrgStructureData function
    await getOrgStructureData(costCenterCodesAgencyCodes, this.resourceService)

    // Map assignments to include updated org structure data
    await Promise.all(
      assignments.map(async (item) => {
        const assignmentStartDate = item.startDate.split('T')[0]
        const assignmentDueDate = item.dueDate.split('T')[0]
        const calendarStartDate =
          calculateDaysDifference(assignmentStartDate, startDate) < 0 ? startDate : assignmentStartDate
        const calendarDueDate = calculateDaysDifference(endDate, assignmentDueDate) > 0 ? assignmentDueDate : endDate

        item.x = calculateDaysDifferenceCoordinates(assignmentStartDate, startDate)
        item.y = 0
        item.width = Math.abs(calculateDaysDifference(calendarStartDate, calendarDueDate)) + 1
        item.startDate = assignmentStartDate
        item.dueDate = assignmentDueDate
        item.costCenterName =
          item.costCenterCode && item.agencyCode
            ? costCenterCodesAgencyCodes[`${item.agencyCode}-${item.costCenterCode}`]?.costCenterName
            : ''
        // agency name
        let agencyName = ''
        if (item.agencyCode && item.costCenterCode) {
          agencyName = costCenterCodesAgencyCodes[`${item.agencyCode}-${item.costCenterCode}`]?.agencyName || ''
        } else if (item.agencyCode && !item.costCenterCode) {
          agencyName = costCenterCodesAgencyCodes[item.agencyCode]?.agencyName || ''
        }
        item.agencyName = agencyName

        const project = allProjects.find((proj) => proj.projectId === item.projectId)
        if (project) {
          // If project is found among allProjects in memory
          item.projectAgencyCode = project.agencyCode
          item.projectBrandCode = project.brandId
          const agencyData = agenciesData[item.projectAgencyCode]
          item.projectAgencyName = agencyData ? agencyData.agencyName : 'Agency not found'
        } else {
          // If project is NOT found among allProjects in memory, use fallback Agency not found
          item.projectAgencyName = 'Agency not found'
        }

        this.changeAssignmentStateIfNeeded(item, projects)
      })
    )
    // Filter out assignments that are placeholders and have no associated project with logged in project manager
    const filteredAssignments = assignments.filter((x) => {
      if (x.isPlaceholder) {
        if (this.userSessionContext.profile === USER_ROLE_PROJECT_MANAGER) {
          // If the assignment is a placeholder, check if the project belongs to project manager
          const projectUser = projects.find((p) => p.projectId === x.projectId)
          if (!projectUser) {
            // If no project is found for the placeholder assignment, skip it
            return false
          }
        }
      }
      // If the assignment is not a placeholder, keep it
      return true
    })

    return filteredAssignments as AssignmentDto[]
  }

  async updateAssignment(assignment: UpdateAssignmentRequest) {
    if (assignment.agencyCode !== '' && assignment.costCenterCode !== '') {
      // Parallelize independent API calls for better performance
      const [workFrontCostCenter, workFrontAgency, workFrontLocation] = await Promise.all([
        this.resourcesPlatformRepository.getWorkFrontData(assignment.costCenterCode, 'CostCenter', 'WorkFront'),
        this.resourcesPlatformRepository.getWorkFrontData(assignment.agencyCode, 'Agency', 'WorkFront'),
        this.resourcesPlatformRepository.getWorkFrontData(assignment.locationName, 'LOCATION', 'WorkFront')
      ])

      assignment.costCenterWorkFrontId =
        workFrontCostCenter && workFrontCostCenter.length > 0 ? workFrontCostCenter[0].ppmEntityID : ''

      assignment.agencyWorkFrontId = workFrontAgency && workFrontAgency.length > 0 ? workFrontAgency[0].ppmEntityID : ''

      assignment.locationWorkFrontId =
        workFrontLocation && workFrontLocation.length > 0 ? workFrontLocation[0].ppmEntityID : ''
    }
    return this.assignmentRepository.updateAssignment(assignment)
  }

  async getAssignmentById(assignmentId: string) {
    const assignment: AssignmentDto = await this.assignmentRepository.getAssignmentById(assignmentId)
    const project = await this.projectsRepository.getProjectsByProjectId(assignment.projectId)

    if (project[0]) {
      const agencyRule = await this.resourcesPlatformRepository.getResourceAgencyRules(project[0].agencyCode)
      // If project is found among allProjects in memory
      assignment.projectAgencyCode = project[0].agencyCode
      assignment.projectBrandCode = project[0].brandId
      assignment.projectAgencyName = agencyRule ? agencyRule.agencyName : 'Agency not found'
    } else {
      // If project is NOT found among allProjects in memory, use fallback Agency not found
      assignment.projectAgencyName = 'Agency not found'
    }

    assignment.width = assignment.totalDays
    assignment.y = 0

    return assignment
  }

  async approveRejectAssignment(params: ApproveRejectAssignment) {
    return this.assignmentRepository.approveRejectAssignment(params)
  }

  async createAssignment(params: CreateAssignmentRequestMutation): Promise<AssignmentDto> {
    // Parallelize independent API calls for better performance
    const [costCenter, agency, location] = await Promise.all([
      this.getWorkFrontData(params.costCenterCode, 'CostCenter'),
      this.getWorkFrontData(params.agencyCode, 'Agency'),
      this.getWorkFrontData(params.locationName, 'LOCATION')
    ])

    params.costCenterWorkFrontId = costCenter
    params.agencyWorkFrontId = agency
    params.locationWorkFrontId = location

    const newAssignment = (await this.assignmentRepository.createAssignment(params)) as AssignmentDto
    this.calculateAssignmentDates(newAssignment, params)
    return newAssignment
  }

  async deleteAssignmentById(assignmentId: string, userLoggedInExternalId: string) {
    return this.assignmentRepository.deleteAssignmentById(assignmentId, userLoggedInExternalId)
  }

  private async getAllProjects(assignments: AssignmentDto[], projects: Project[]): Promise<Project[]> {
    const BATCH_SIZE = 25
    const unfetchedProjectIds = new Set(
      assignments
        .filter((assignment) => !projects.some((project) => project.projectId === assignment.projectId))
        .map((assignment) => assignment.projectId)
    )

    // Check if there are any unfetched project IDs
    if (unfetchedProjectIds.size === 0) {
      return projects
    }

    const unfetchedProjects: Project[] = []
    const projectIdsArray = Array.from(unfetchedProjectIds)

    // Process in batches
    for (let i = 0; i < projectIdsArray.length; i += BATCH_SIZE) {
      const batch = projectIdsArray.slice(i, i + BATCH_SIZE)
      const batchProjects = await this.projectsRepository.getProjectsByProjectId(batch.join(','))
      if (Array.isArray(batchProjects)) {
        unfetchedProjects.push(...batchProjects)
      }
    }

    return projects.concat(unfetchedProjects)
  }

  private async fetchProjectsIfNeeded(workCode: string): Promise<Project[]> {
    if (this.userSessionContext.profile === USER_ROLE_PROJECT_MANAGER) {
      return await this.projectsRepository.getProjectsByProjectManagerWorkCode(workCode)
    }
    return []
  }

  private changeAssignmentStateIfNeeded(assignment: AssignmentDto, projects: Project[]): void {
    // Check if the assignment is a placeholder or if the user is not a project manager
    if (assignment.isPlaceholder || this.userSessionContext.profile !== USER_ROLE_PROJECT_MANAGER) {
      return
    }

    // If no associated project is found, update the assignment state
    const projectUser = projects.find((p) => p.projectId === assignment.projectId)
    if (!projectUser) {
      assignment.assignmentState = assignment.taskState
      assignment.taskState = 'External'
      //assignment.assignmentState = 'External'
    }
  }
  private async getWorkFrontData(code: string, type: string): Promise<string> {
    const data = await this.resourcesPlatformRepository.getWorkFrontData(code, type, 'WorkFront')
    return data && data.length > 0 ? data[0].ppmEntityID : ''
  }

  private calculateAssignmentDates(assignment: AssignmentDto, params: CreateAssignmentRequestMutation): void {
    const assignmentStartDate = assignment.startDate.split('T')[0]
    const assignmentDueDate = assignment.dueDate.split('T')[0]
    const calendarStartDate =
      calculateDaysDifference(assignmentStartDate, params.calendarStartDate) < 0
        ? params.calendarStartDate
        : assignmentStartDate
    const calendarDueDate =
      calculateDaysDifference(params.calendarDueDate, assignmentDueDate) > 0
        ? assignmentDueDate
        : params.calendarDueDate

    assignment.x = calculateDaysDifferenceCoordinates(assignmentStartDate, params.calendarStartDate)
    assignment.y = 0
    assignment.width = Math.abs(calculateDaysDifference(calendarStartDate, calendarDueDate)) + 1
    assignment.startDate = assignmentStartDate
    assignment.dueDate = assignmentDueDate
  }
}

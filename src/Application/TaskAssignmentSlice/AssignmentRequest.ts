export interface AssignmentRequest {
  startDate: string
  endDate: string
  projectIds: string[]
  userIds: string[]
  workCode: string
  agencyCode?: string[]
  costCenterCode?: string[]
  location?: string[]
  brandIds: string[]
  taskIds: string[]
  taskStates: string[]
  usersGroupIds: number[]
  userLoggedInExternalId: string
  userRole: string
  isExpandedPlaceholderApplied?: boolean
  resourceAgencyCode?: string[]
  resourceCostCenterCode?: string[]
  resourceLocation?: string[]
}

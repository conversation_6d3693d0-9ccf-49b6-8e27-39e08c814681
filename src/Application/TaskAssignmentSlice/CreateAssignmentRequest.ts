import { NonWorkingDate } from '../../Domain/TaskAssignmentSlice/NonWorkingDate'

export interface CreateAssignmentRequest {
  taskExternalId: string
  userAssignedExternalId: string
  userLoggedInExternalId: string
  startDate: string
  dueDate: string
  hoursPerDay: number
  isSplit: boolean
  createAssignmentDate: string
  agencyName: string
  locationName: string
  costCenterName: string
  agencyCode: string
  costCenterCode: string
  agencyWorkFrontId: string
  costCenterWorkFrontId: string
  locationWorkFrontId: string
  nonWorkingDates: NonWorkingDate[]
}

export interface CreateAssignmentRequestMutation extends CreateAssignmentRequest {
  calendarStartDate: string
  calendarDueDate: string
}

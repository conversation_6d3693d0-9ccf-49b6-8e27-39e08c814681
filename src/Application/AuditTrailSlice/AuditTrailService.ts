import { inject, injectable } from 'tsyringe'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'
import { IAuditTrailRepository } from './IAuditTrailRepository'

@injectable()
export default class AuditTrailService {
  constructor(
    @inject('IAuditTrailRepository')
    private assignmentRepository: IAuditTrailRepository,
    @inject(UserSessionContext)
    protected userSessionContext: UserSessionContext
    // Inject the repository interface
  ) {}

  async getAuditTrailForLoggedInUser(userLoggedInExternalId: string) {
    return this.assignmentRepository.getAuditTrailForLoggedInUser(userLoggedInExternalId)
  }

  async clearAuditTrailForLoggedInUser(userLoggedInExternalId: string) {
    return this.assignmentRepository.clearAuditTrailForLoggedInUser(userLoggedInExternalId)
  }
}

import { inject, injectable } from 'tsyringe'
import { UserInfo } from '../../Domain/UserInfoSlice/UserInfo'
import { IUserInfoRepository } from './UserInfoRepository'

@injectable()
export default class UserInforService {
  constructor(@inject('IUserInfoRepository') private userRepository: IUserInfoRepository) {}

  async getUserInfo(): Promise<UserInfo> {
    return await this.userRepository.getUserInfo()
  }

  async getUserByLLID(llid: string): Promise<UserInfo> {
    return await this.userRepository.getUserByLLID(llid)
  }
}

import { inject, injectable } from 'tsyringe'
import { IUsersGroupRepository } from './IUsersGroupRepository'
import { UsersGroupRequest } from './UsersGroupRequest'

@injectable()
export default class UsersGroupService {
  constructor(
    @inject('IUsersGroupRepository')
    private usersGroupsRepository: IUsersGroupRepository
  ) {}

  async getUsersGroups(params: UsersGroupRequest) {
    return this.usersGroupsRepository.getUsersGroups(params)
  }
}

import { inject, injectable } from 'tsyringe'
import { Workfront } from '../../Domain/WorkfrontSlice/Workfront'
import { IWorkfrontRepository } from './WorkfrontRepository'
import { GetWorkfrontUserPayload } from './WorkfrontRequests'

@injectable()
export default class WorkfrontService {
  constructor(@inject('IWorkfrontRepository') private workfrontRepository: IWorkfrontRepository) {}

  async getWorkfrontUser(payload: GetWorkfrontUserPayload): Promise<Workfront> {
    return await this.workfrontRepository.getWorkfrontUser(payload)
  }
}

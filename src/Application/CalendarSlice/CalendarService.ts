import dayjs from 'dayjs'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { inject, injectable } from 'tsyringe'
import { MonthData } from '../../Domain/CalendarSlice/MonthData'
import { DayData } from '../../Domain/CalendarSlice/DayData'
import { CalendarRequest } from './CalendarRequest'
import ILogger from '../../Domain/Common/ILogger'

@injectable()
export class CalendarService {
  constructor(@inject('ILogger') private logger: ILogger) {
    dayjs.extend(isSameOrBefore)
  }

  async getCalendarData(request: CalendarRequest): Promise<MonthData[]> {
    const { startDate, endDate } = request
    const start = dayjs(startDate)
    const end = dayjs(endDate)

    if (start.isAfter(end)) {
      this.logger.error('startDate must be before endDate')
      throw new Error('startDate must be before endDate')
    }

    const calendarData: MonthData[] = []
    let currentDay = start

    while (currentDay.isSameOrBefore(end)) {
      const currentMonth = currentDay.format('MMMM')
      const monthIndex = calendarData.findIndex((monthData) => monthData.month === currentMonth)

      const dayData: DayData = {
        dayShortName: currentDay.format('dd'),
        dayMiddleName: currentDay.format('ddd'),
        dayNumber: currentDay.date(),
        date: currentDay.format('YYYY-MM-DD'),
        isWeekend: currentDay.day() === 0 || currentDay.day() === 6
      }

      if (monthIndex === -1) {
        calendarData.push({
          month: currentMonth,
          days: [dayData]
        })
      } else {
        calendarData[monthIndex].days.push(dayData)
      }

      currentDay = currentDay.add(1, 'day')
    }

    return calendarData
  }
}

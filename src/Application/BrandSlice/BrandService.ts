import { inject, injectable } from 'tsyringe'
import { IBrandRepository } from './BrandRepository'
import { Brand } from '../../Domain/BrandSlice/Brand'
import { orderBy } from 'lodash'

@injectable()
export default class BrandService {
  constructor(
    @inject('IBrandRepository')
    private brandRepository: IBrandRepository
  ) {}

  async getBrands(params: string[]): Promise<Brand[]> {
    const brands = await this.brandRepository.getBrands(params)
    return orderBy(brands, ['brandName'], ['asc'])
  }
}

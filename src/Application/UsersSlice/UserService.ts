import { User } from '../../Domain/UsersSlice/User'
import { IUserRepository } from './IUserRepository'
import { inject, injectable } from 'tsyringe'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'

@injectable()
export default class UserService {
  constructor(
    @inject('IUserRepository')
    private usersPlatformRepository: IUserRepository,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {}

  async getUserByLlid(llid: string): Promise<User> {
    return this.usersPlatformRepository.getUserByLlid(llid)
  }
}

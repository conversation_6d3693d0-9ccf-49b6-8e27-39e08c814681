import { inject, injectable } from 'tsyringe'
import { ITimesheetsRepository } from './TimesheetsRepository'
import { TimeOffDetails, Timesheets } from '../../Domain/TimesheetsSlice/Timesheets'
import { TimeOffRequest, TimesheetsRequest } from './TimesheetsRequests'

@injectable()
export default class TimesheetsService {
  constructor(
    @inject('ITimesheetsRepository')
    private timesheetsRepository: ITimesheetsRepository
  ) {}

  // Using mock data for now. Uncomment the following lines to use the repository method.
  // async getTimesheetsForResource(params: TimesheetsRequest): Promise<Timesheets[]> {
  //   const timesheets = await this.timesheetsRepository.getTimesheetsForResource(params)
  //   return timesheets
  // }

  async getTimesheetsForResource(params: TimesheetsRequest): Promise<Timesheets[]> {
    // Mock implementation
    if (params) {
      return [
        {
          project: 'Project Alpha',
          task: 'Development',
          user: '<PERSON>',
          timesheetDate: '2023-01-04',
          timesheetStatus: 'Approved',
          hours: 8
        },
        {
          project: 'Project Beta',
          task: 'Testing',
          user: '<PERSON>e',
          timesheetDate: '2023-01-03',
          timesheetStatus: 'Rejected',
          hours: 6
        },
        {
          project: 'Project Beta',
          task: 'Testing',
          user: 'John Doe',
          timesheetDate: '2023-01-02',
          timesheetStatus: 'Submitted',
          hours: 3
        },
        {
          project: 'Project Gamma',
          task: 'Testing',
          user: 'Jane Smith',
          timesheetDate: '2023-01-01',
          timesheetStatus: 'Unsubmitted',
          hours: 7
        }
      ]
    }
  }

  async getTimeOffForResource(params: TimeOffRequest): Promise<TimeOffDetails[]> {
    const timeOffDetails = await this.timesheetsRepository.getTimeOffForResource(params)
    return timeOffDetails
  }
}

import { ResourceAndPlaceholderRequest } from './ResourceAndPlaceholderRequest'
import { inject, injectable } from 'tsyringe'
import { UserSessionContext } from '../AuthSlice/UserSessionContext'
import { ResourceAndPlaceholderStrategyFactory } from './ResourceAndPlaceholderStrategyFactory'
import { mapToUserDto } from './ResourceAndPlaceholderMapping'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourceAndPlaceholderRepository } from './IResourceAndPlaceholderRepository'
import { ResourceAndPlaceholderDto } from './ResourceAndPlaceholderDto'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import ILogger from '../../GraphQL/Common/Logger'
@injectable()
export default class ResourceAndPlaceholderService {
  constructor(
    @inject('IResourcePlatformRepository')
    private platformRepository: IResourcePlatformRepository,
    @inject('IResourceAndPlaceholderRepository')
    private resourceAndPlaceholderRepository: IResourceAndPlaceholderRepository,
    @inject('IProjectRepository')
    private projectsRepository: IProjectRepository,
    @inject('ILogger')
    private logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {}

  async getResourcesAndPlaceholders(params: ResourceAndPlaceholderRequest): Promise<ResourceAndPlaceholderDto[]> {
    const userProfile = this.userSessionContext.profile
    const strategy = ResourceAndPlaceholderStrategyFactory.getStrategy(
      userProfile,
      this.platformRepository,
      this.resourceAndPlaceholderRepository,
      this.projectsRepository,
      this.logger
    )

    const rmUsers = await strategy.fetchUsers(params)

    if (!rmUsers) {
      throw new Error('Invalid users data. Try again.')
    }

    // Merge both datasets into ResourceDto
    const mergedUsers = rmUsers.map((placeholder) => {
      return mapToUserDto(placeholder)
    })

    mergedUsers.sort((a, b) => {
      if (a.isPlaceholder === b.isPlaceholder) {
        return a.name.localeCompare(b.name)
      }
      return a.isPlaceholder ? -1 : 1
    })

    return mergedUsers
  }
}

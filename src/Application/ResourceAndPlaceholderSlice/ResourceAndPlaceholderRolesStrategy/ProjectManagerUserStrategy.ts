import { ResourceAndPlaceholder } from '../../../Domain/ResourceAndPlaceholderSlice/ResourceAndPlaceholder'
import { ResourceAndPlaceholderRequest } from '../ResourceAndPlaceholderRequest'
import { IResourceAndPlaceholderStrategy } from './IResourceAndPlaceholderStrategy'
import { IProjectRepository } from '../../ProjectSlice/IProjectRepository'
import { IResourceAndPlaceholderRepository } from '../IResourceAndPlaceholderRepository'
import {
  mapPlaceholderByWorkCodeToResourceAndPlaceholder,
  mapToResourceAndPlaceholder
} from '../ResourceAndPlaceholderMapping'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'
import FetchRequestFailedError from '../../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import ILogger from '../../../Domain/Common/ILogger'

export class ProjectManagerUserStrategy implements IResourceAndPlaceholderStrategy {
  constructor(
    private resourceAndPlaceholderRepository: IResourceAndPlaceholderRepository,
    private projectsRepository: IProjectRepository,
    private platformRepository: IResourcePlatformRepository,
    private logger: ILogger // Inject ILogger
  ) {}

  async fetchUsers(params: ResourceAndPlaceholderRequest): Promise<ResourceAndPlaceholder[]> {
    return params.searchName === ''
      ? this.fetchByProjectManager(params.userId)
      : this.fetchBySearchName(params.searchName)
  }

  private async fetchByProjectManager(userId: string): Promise<ResourceAndPlaceholder[]> {
    try {
      const projects = await this.projectsRepository.getProjectsByProjectManagerWorkCode(userId)
      if (!projects) {
        // No projects found (404 or empty result)
        this.logger.info('No projects found for this project manager (empty result).')
        return []
      }
      const projectIds = projects.map((project) => project.projectId)
      return this.resourceAndPlaceholderRepository.getResourcesAndPlaceholdersByProjectManager(userId, projectIds)
    } catch (error) {
      // Handle FetchRequestFailedError or any other error
      if (error instanceof FetchRequestFailedError && error.httpStatusCode === 404) {
        // Custom handling for 404
        this.logger.info('Returning empty ResourceAndPlaceholder list as a result.')
        return []
      }
      this.logger.error('Error fetching resources and placeholders for project manager:', error)
      throw new Error('Invalid projects data. Try again.')
    }
  }

  private async fetchBySearchName(searchName: string): Promise<ResourceAndPlaceholder[]> {
    const users = await this.platformRepository.getUsersBySearchName(searchName)
    const placeholders = await this.platformRepository.getPlaceholderByWorkCode(searchName)
    return [
      ...users.map(mapToResourceAndPlaceholder),
      ...placeholders.map(mapPlaceholderByWorkCodeToResourceAndPlaceholder)
    ]
  }
}

import { ResourceAndPlaceholder } from '../../../Domain/ResourceAndPlaceholderSlice/ResourceAndPlaceholder'
import { prepareOrgStructureData } from '../../Common/Utils/PrepareOrgStructureData'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'
import { IResourceAndPlaceholderRepository } from '../IResourceAndPlaceholderRepository'
import { mapPlaceholderByWorkCodeToResourceAndPlaceholder } from '../ResourceAndPlaceholderMapping'
import { ResourceAndPlaceholderRequest } from '../ResourceAndPlaceholderRequest'
import { IResourceAndPlaceholderStrategy } from './IResourceAndPlaceholderStrategy'

export class ResourceManagerUserStrategy implements IResourceAndPlaceholderStrategy {
  constructor(
    private resourceAndPlaceholderRepository: IResourceAndPlaceholderRepository,
    private resourcesPlatformRepository: IResourcePlatformRepository
  ) {}

  async fetchUsers(params: ResourceAndPlaceholderRequest): Promise<ResourceAndPlaceholder[]> {
    return params.searchName === ''
      ? this.fetchByResourceManager(params.userId)
      : this.fetchBySearchName(params.searchName, params.userId)
  }

  private async fetchByResourceManager(userId: string): Promise<ResourceAndPlaceholder[]> {
    const { agencyCodes, costCenterCodes, costCenterCities } = await prepareOrgStructureData(
      this.resourcesPlatformRepository,
      userId
    )
    return await this.resourceAndPlaceholderRepository.getResourcesAndPlaceholdersByResourceManager(
      userId,
      agencyCodes,
      costCenterCodes,
      costCenterCities
    )
  }

  private async fetchBySearchName(searchName: string, userId: string): Promise<ResourceAndPlaceholder[]> {
    const users = await this.fetchByResourceManager(userId)
    const placeholders = await this.resourcesPlatformRepository.getPlaceholderByWorkCode(searchName)
    return [...users, ...placeholders.map(mapPlaceholderByWorkCodeToResourceAndPlaceholder)]
  }
}

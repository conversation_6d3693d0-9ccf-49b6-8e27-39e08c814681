import { Employee } from '../../Domain/ResourceAndPlaceholderSlice/Employee'
import { ResourceAndPlaceholder } from '../../Domain/ResourceAndPlaceholderSlice/ResourceAndPlaceholder'
import { PlaceholderByWorkCode } from '../../Domain/ResourceSlice/PlaceholderByWorkCode'
import { ResourceAndPlaceholderDto } from './ResourceAndPlaceholderDto'

export function mapToUserDto(user: ResourceAndPlaceholder): ResourceAndPlaceholderDto {
  // Map fields according to the GraphQL schema structure
  return {
    id: user.externalId, // Assuming the `id` comes from externalId
    name: user.name,
    isPlaceholder: user.isPlaceholder,
    integrationId: user.integrationId
  }
}

export function mapToResourceAndPlaceholder(user: Employee): ResourceAndPlaceholder {
  // Map fields according to the GraphQL schema structure
  return {
    externalId: user.employeeCode,
    name: user.employeeFirstName + ' ' + user.employeeLastName,
    isPlaceholder: false,
    integrationId: ''
  }
}

export function mapPlaceholderByWorkCodeToResourceAndPlaceholder(
  placeholder: PlaceholderByWorkCode
): ResourceAndPlaceholder {
  // Map fields according to the GraphQL schema structure
  return {
    externalId: placeholder.workCode,
    name: placeholder.workCode + ' : ' + placeholder.workCodeName,
    isPlaceholder: true,
    integrationId: ''
  }
}

import { ResourceAndPlaceholder } from '../../Domain/ResourceAndPlaceholderSlice/ResourceAndPlaceholder'

export interface IResourceAndPlaceholderRepository {
  getResourcesAndPlaceholdersByProjectManager(userId: string, projectIds: string[]): Promise<ResourceAndPlaceholder[]>
  getResourcesAndPlaceholdersByResourceManager(
    resourceManagerId: string,
    agencyCode: string[],
    costCenterCode: string[],
    location: string[]
  ): Promise<ResourceAndPlaceholder[]>
  searchResourcesAndPlaceholders(searchName: string): Promise<ResourceAndPlaceholder[]>
}

import { USER_ROLE_PROJECT_MANAGER, USER_ROLE_RESOURCE_MANAGER } from '../../Constants/user_roles'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import { IResourceAndPlaceholderRepository } from './IResourceAndPlaceholderRepository'
import { IResourceAndPlaceholderStrategy } from './ResourceAndPlaceholderRolesStrategy/IResourceAndPlaceholderStrategy'
import { ProjectManagerUserStrategy } from './ResourceAndPlaceholderRolesStrategy/ProjectManagerUserStrategy'
import { ResourceManagerUserStrategy } from './ResourceAndPlaceholderRolesStrategy/ResourceManagerUserStrategy'
import ILogger from '../../Domain/Common/ILogger'
export class ResourceAndPlaceholderStrategyFactory {
  static getStrategy(
    userProfile: string,
    platformRepository: IResourcePlatformRepository,
    resourceAndPlaceholderRepository: IResourceAndPlaceholderRepository,
    projectsRepository: IProjectRepository,
    logger: ILogger // Add logger parameter
  ): IResourceAndPlaceholderStrategy {
    switch (userProfile) {
      case USER_ROLE_PROJECT_MANAGER:
        return new ProjectManagerUserStrategy(
          resourceAndPlaceholderRepository,
          projectsRepository,
          platformRepository,
          logger
        )

      case USER_ROLE_RESOURCE_MANAGER:
        return new ResourceManagerUserStrategy(resourceAndPlaceholderRepository, platformRepository)

      default:
        throw new Error(`Unknown user profile: ${userProfile}`)
    }
  }
}

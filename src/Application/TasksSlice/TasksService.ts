import { inject, injectable } from 'tsyringe'
import { ITasksRepository } from './ITasksRepository'
import { Task } from '../../Domain/TasksSlice/Task'
import { ProjectWithTasks } from '../../Domain/ProjectSlice/ProjectWithTasks'
import { orderBy } from 'lodash'
import { IProjectRMRepository } from '../ProjectSlice/IProjectRMRepository'
import { IProjectRepository } from '../ProjectSlice/IProjectRepository'

@injectable()
export default class TasksService {
  constructor(
    @inject('ITasksRepository')
    private tasksRepository: ITasksRepository,
    @inject('IProjectRMRepository')
    private projectRMRepository: IProjectRMRepository,
    @inject('IProjectRepository')
    private projectRepository: IProjectRepository
  ) {}

  async getTasksByProjectId(projectId: string): Promise<Task[]> {
    const tasks = await this.tasksRepository.getTasksByProjectId(projectId)
    return orderBy(tasks, ['taskName'], ['asc'])
  }

  async getTaskByProjectIdAndTaskId(projectId: string, taskId: string): Promise<Task[]> {
    return await this.tasksRepository.getTaskByProjectIdAndTaskId(projectId, taskId)
  }

  async getPMTasksByGroupOfProjects(projectIds: string[], workCode: string): Promise<ProjectWithTasks[]> {
    let projects = await this.projectRepository.getProjectsByProjectManagerWorkCode(workCode)

    if (projects && projects.length === 0) {
      return []
    }

    if (projectIds.length > 0) {
      projects = projects.filter((project) => projectIds.includes(project.projectId))
    }

    const projectIdList = projects.map((project) => project.projectId)
    const tasks = await this.projectRMRepository.getTasksByProjectList(projectIdList)
    const mainArray = projects.map((project) => ({
      id: project.projectId,
      name: project.name,
      tasks: tasks.filter((task) => task.projectId === project.projectId)
    }))
    const sortedArray = this.sortProjectWithTasks(mainArray)
    return sortedArray.filter((project) => project.tasks.length > 0)
  }

  async getRMTasksByGroupOfProjects(projectIds: string[], workCode: string): Promise<ProjectWithTasks[]> {
    const mainArray: ProjectWithTasks[] = []
    let projects = await this.projectRMRepository.getProjectsByResourceManagerId(workCode, false)

    if (projectIds.length > 0) {
      projects = projects.filter((project) => projectIds.includes(project.id))
    }

    for (const project of projects) {
      const tasks = await this.tasksRepository.getTasksByProjectId(project.id)
      mainArray.push({
        id: project.id,
        name: project.name,
        tasks: tasks
      })
    }
    return this.sortProjectWithTasks(mainArray)
  }

  private sortProjectWithTasks(ProjectWithTasks: ProjectWithTasks[]): ProjectWithTasks[] {
    // Sort the main array by project name and tasks by task name
    ProjectWithTasks = orderBy(ProjectWithTasks, ['name'], ['asc'])

    ProjectWithTasks.forEach((item) => {
      if (Array.isArray(item.tasks)) {
        item.tasks = orderBy(item.tasks, ['taskName'], ['asc'])
      }
    })
    return ProjectWithTasks
  }
}

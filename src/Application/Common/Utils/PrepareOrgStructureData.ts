import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'

export async function prepareOrgStructureData(
  resourcesPlatformRepository: IResourcePlatformRepository,
  userId: string
) {
  const resource = await resourcesPlatformRepository.getResourceById(userId)
  const orgStructure = await resourcesPlatformRepository.getUserOrgStructure(
    resource.jobInformation.agencyCode,
    resource.jobInformation.costCenterCode
  )

  const agencyCodes = [resource.jobInformation.agencyCode]
  const costCenterCodes = [resource.jobInformation.costCenterCode]
  const costCenterCities = [orgStructure[0]?.profitCenters?.[0]?.costCenters?.[0]?.costCenterCity ?? '']

  return { agencyCodes, costCenterCodes, costCenterCities }
}

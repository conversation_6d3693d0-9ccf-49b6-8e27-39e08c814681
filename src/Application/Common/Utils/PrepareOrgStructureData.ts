import { Agency } from '../../../Domain/ResourceSlice/Agency'
import { CostCenter } from '../../../Domain/ResourceSlice/CostCenter'
import { IResourcePlatformRepository } from '../../ResourceSlice/IResourcePlatformRepository'

export async function prepareOrgStructureData(
  resourcesPlatformRepository: IResourcePlatformRepository,
  userId: string
) {
  const resource = await resourcesPlatformRepository.getResourceById(userId)
  const orgStructure = await resourcesPlatformRepository.getUserOrgStructure(
    resource.jobInformation.agencyCode,
    resource.jobInformation.costCenterCode
  )

  const agencyCodes = [resource.jobInformation.agencyCode]
  const costCenterCodes = [resource.jobInformation.costCenterCode]
  const costCenterCities = [orgStructure[0]?.profitCenters?.[0]?.costCenters?.[0]?.costCenterCity ?? '']

  return { agencyCodes, costCenterCodes, costCenterCities }
}

export async function prepareAgenciesData(
  resourcesPlatformRepository: IResourcePlatformRepository,
  agencyCodes: string[]
): Promise<Agency[]> {
  const agenciesData = await resourcesPlatformRepository.getAgenciesData(agencyCodes)
  const agencies = agenciesData.map((agency: { agency: { agencyCode: string; agencyName: string } }) => ({
    agencyCode: agency.agency.agencyCode,
    agencyName: agency.agency.agencyName
  }))
  return agencies.sort((a, b) => a.agencyName.localeCompare(b.agencyName))
}

export async function prepareCostCentersData(
  resourcesPlatformRepository: IResourcePlatformRepository,
  costCenterCodes: string[]
): Promise<CostCenter[]> {
  const orgStructures = await Promise.all(
    costCenterCodes.map((costCenterCode) =>
      resourcesPlatformRepository.getUserOrgStructureByCostCenterCode(costCenterCode)
    )
  )

  // Filter out invalid orgStructure responses
  const validOrgStructures = orgStructures.filter(
    (orgStructure) => Array.isArray(orgStructure) && orgStructure.every((item) => item && item.profitCenters)
  )

  if (validOrgStructures.length === 0) {
    return []
  }

  const costCenters = validOrgStructures.flatMap((orgStructureArr) =>
    orgStructureArr.flatMap((orgStructure) =>
      orgStructure.profitCenters.flatMap((profitCenter) =>
        profitCenter.costCenters.map((costCenter) => ({
          costCenterName: costCenter.costCenterName,
          costCenterCode: costCenter.costCenterCode,
          costCenterCity: costCenter.costCenterCity
        }))
      )
    )
  )

  const uniqueCostCenters = costCenters
    .filter(
      (costCenter, index, self) =>
        index ===
        self.findIndex(
          (c) => c.costCenterCode === costCenter.costCenterCode && c.costCenterName === costCenter.costCenterName
        )
    )
    .sort((a, b) => a.costCenterName.localeCompare(b.costCenterName))

  return uniqueCostCenters
}

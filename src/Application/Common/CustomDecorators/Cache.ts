import { container } from 'tsyringe'
import { ICacheService } from '../ICacheService'
import { CacheProps } from '../../../Domain/Common/CacheProps'

/* eslint-disable @typescript-eslint/no-explicit-any */
export function cache(cacheKeyGenerator: (...args: any[]) => CacheProps) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheService = container.resolve<ICacheService>('ICacheService')
      const { module, key, cacheDuration } = cacheKeyGenerator(...args) as CacheProps
      const cacheKey = `${key}|${module}`
      const cachedValue = await cacheService.getCacheValue<any>(cacheKey)
      if (cachedValue) {
        return cachedValue
      }

      const result = await originalMethod.apply(this, args)

      await cacheService.setCacheValue(cacheKey, result, cacheDuration)
      await cacheService.addValueToKeyVault(key, cacheKey)
      return result
    }

    return descriptor
  }
}

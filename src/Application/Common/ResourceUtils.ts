import { AgencyRule } from './../../Domain/ResourceSlice/AgencyRule'

import { DEFAULT_MINIMUM_WORKING_HOURS } from '../../Constants/minimum_working_hours'
import { IResourcePlatformRepository } from '../ResourceSlice/IResourcePlatformRepository'
import ResourceService from '../ResourceSlice/ResourceService'

export async function getAgency(
  agencyCode: string,
  agenciesData: { [key: string]: AgencyRule },
  resourcesPlatformRepository: IResourcePlatformRepository
): Promise<AgencyRule> {
  async function getAgencyCached(agencyCode: string): Promise<AgencyRule> {
    if (agenciesData[agencyCode] !== undefined) {
      return agenciesData[agencyCode]
    } else {
      const agencyData = await getAgencyData(agencyCode, resourcesPlatformRepository)
      agenciesData[agencyCode] = agencyData
      return agencyData
    }
  }

  return getAgencyCached(agencyCode)
}

export async function getAgencies(
  agencyCodes: string[],
  agenciesData: { [key: string]: AgencyRule },
  resourcesPlatformRepository: IResourcePlatformRepository
): Promise<AgencyRule[]> {
  const agencyRules: AgencyRule[] = []
  const uncachedAgencyCodes: string[] = []

  // Identify uncached agency codes
  for (const agencyCode of agencyCodes) {
    if (agenciesData[agencyCode] !== undefined) {
      agencyRules.push(agenciesData[agencyCode])
    } else {
      uncachedAgencyCodes.push(agencyCode)
    }
  }

  // Fetch data for uncached agency codes
  if (uncachedAgencyCodes.length > 0) {
    try {
      const fetchedAgencies = await getAgenciesData(uncachedAgencyCodes, resourcesPlatformRepository)
      for (const agency of fetchedAgencies) {
        agenciesData[agency.agencyCode] = agency
        agencyRules.push(agency)
      }
    } catch (error) {
      console.error('Error fetching agency data:', error)
    }
  }

  return agencyRules
}

async function getAgencyData(
  agencyCode: string,
  resourcesPlatformRepository: IResourcePlatformRepository
): Promise<AgencyRule> {
  try {
    const agencyRule = await resourcesPlatformRepository.getResourceAgencyRules(agencyCode)
    return {
      minimumAgencyHoursPerDay: agencyRule?.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS,
      agencyName: agencyRule?.agencyName ?? 'Agency not found',
      agencyCode: agencyRule?.agencyCode ?? 'Agency not found'
    }
  } catch (error) {
    throw new Error(`Agency not found for agencyCode: ${agencyCode}`)
  }
}

async function getAgenciesData(
  agencyCodes: string[],
  resourcesPlatformRepository: IResourcePlatformRepository
): Promise<AgencyRule[]> {
  try {
    const agencyRules = await resourcesPlatformRepository.getResourceAgenciesRules(agencyCodes)
    return agencyRules.map((agencyRule) => ({
      minimumAgencyHoursPerDay: agencyRule?.minimumAgencyHoursPerDay ?? DEFAULT_MINIMUM_WORKING_HOURS,
      agencyName: agencyRule?.agencyName ?? 'Agency not found',
      agencyCode: agencyRule?.agencyCode ?? 'Agency not found'
    }))
  } catch (error) {
    throw new Error(`Agencies not found for agencyCodes: ${agencyCodes.join(', ')}`)
  }
}

export async function getOrgStructureData(
  costCenterCodesAgencyCodes: {
    [key: string]: { costCenterCode: string; agencyCode: string; costCenterName: string; agencyName: string }
  },
  resourceService: ResourceService
): Promise<void> {
  const uncachedEntries = Object.entries(costCenterCodesAgencyCodes)

  const fetchPromises = uncachedEntries.map(async ([key, { costCenterCode, agencyCode }]) => {
    try {
      if (!costCenterCode) {
        const agencyData = await resourceService.getOrgStructureByAgencyCode(agencyCode)
        costCenterCodesAgencyCodes[key] = {
          costCenterName: '',
          agencyName: agencyData.agencyName,
          costCenterCode: '',
          agencyCode: agencyData.agencyCode
        }
      } else if (costCenterCode && agencyCode) {
        const orgStructure = await resourceService.getOrgStructureByAgencyCodeCostCenterCode({
          costCenterCode,
          agencyCode
        })

        costCenterCodesAgencyCodes[key] = {
          costCenterName: orgStructure.costCenterName,
          agencyName: orgStructure.agencyName,
          costCenterCode: orgStructure.costCenterCode,
          agencyCode: orgStructure.agencyCode
        }
      }
    } catch (error) {
      console.error(`Error fetching org structure for ${costCenterCode} and ${agencyCode}:`, error)
    }
  })

  await Promise.all(fetchPromises)
}

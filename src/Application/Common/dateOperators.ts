import dayjs from 'dayjs'

export const calculateDaysDifference = (startDate: string, givenDate: string): number => {
  const start = dayjs(startDate)
  const given = dayjs(givenDate)
  const difference = start.diff(given, 'day')
  return difference
}

export const calculateDaysDifferenceCoordinates = (startDate: string, givenDate: string): number => {
  const start = dayjs(startDate)
  const given = dayjs(givenDate)
  if (start.diff(given, 'day') < 0) return 0
  return start.diff(given, 'day')
}

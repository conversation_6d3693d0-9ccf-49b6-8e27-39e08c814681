import 'reflect-metadata'
import './GraphQL/AuthSlice/injectorConfig'
import './GraphQL/ResourceSlice/injectorConfig'
import './GraphQL/TaskAssignmentSlice/injectorConfig'
import './GraphQL/UsersGroupsSlice/injectorConfig'
import './GraphQL/UsersSlice/injectorConfig'
import './GraphQL/CalendarSlice/injectorConfig'
import './GraphQL/ProjectSlice/injectorConfig'
import './GraphQL/TasksSlice/injectorConfig'
import './GraphQL/Common/injectorConfig'
import './GraphQL/PlaceholderSlice/injectorConfig'
import './GraphQL/ResourceAndPlaceholderSlice/injectorConfig'
import './GraphQL/BrandSlice/injectorConfig'
import './GraphQL/AuditTrailSlice/injectorConfig'
import './GraphQL/TimesheetsSlice/injectorConfig'
import './GraphQL/WorkfrontSlice/injectorConfig'
import './GraphQL/UserInfoSlice/injectorConfig'

import { ApolloServer } from '@apollo/server'
import { startStandaloneServer } from '@apollo/server/standalone'
import { typeDefs, resolvers } from './GraphQL'
import { context } from './context'
import { container } from 'tsyringe'
import LoggingMiddleware from './GraphQL/Common/LoggingMiddleware'
import ILogger from './Domain/Common/ILogger'
// import HttpResponseMiddleware from './GraphQL/Common/HttpResponseMiddleware'
import { handleError } from './GraphQL/Common/DefaultErrorHandler'

const logger = container.resolve<ILogger>('ILogger')

const server = new ApolloServer({
  typeDefs,
  resolvers,
  formatError: (_, err) => handleError(err, logger),
  plugins: [LoggingMiddleware(logger)]
})

const startServer = async () => {
  const { url } = await startStandaloneServer(server, {
    context
  })

  logger.info(`🚀 Server ready at: ${url}`)
}

startServer()

export interface Assignment {
  id: string
  startDate: string
  dueDate: string
  hoursPerDay: number
  resourceId: string
  projectId: string
  taskId: string
  activeNonWorkingDays: string[]
  totalDays: number
  taskName: string
  projectName: string
  externalLink: string
  altairJobNumber?: string
  userId: string
  isPlaceholder: boolean
  state: string
  assignmentState: string
  projectManagerName: string
  taskState: string
  taskNotes: string
  taskStartDate: string
  taskDueDate: string
  assignmentNotes: string
  projectIntegrationId: string
  userIntegrationId: string
  agencyCode: string
  costCenterCode: string
  locationName: string
  assignmentIcaStatus: string
}

export interface UserInfo {
  employeeCode: string
  businessRole: string[]
  userName: string
}

export interface EmployeeDetails {
  jobInformationHistory: unknown[]
  addressHistory: unknown[]
  personalInformationHistory: unknown[]
  workOrderHistory: unknown[]
  compensationHistory: unknown[]
  employeeCode: string
  hireDate: string
  terminationDate: string | null
  communicationLanguage: string
  reasonForAction: string | null
  emailAddress: string
  lionLogin: string
  firstName: string
  lastName: string
  homeState: string
  jobInformation: JobInformation
  address: Address
  personalInformation: PersonalInformation
  workOrder: unknown | null
  compensation: unknown | null
}

export interface JobInformation {
  contractInformationStartDate: string
  contractInformationEndDate: string
  companyCode: string
  costCenterCode: string
  agencyCode: string
  partTimeEmployee: boolean
  contractor: boolean
  workCode: string
  timesheetRequired: boolean
  flsaNonExempt: boolean
  contractStatus: string
  businessTitle: string
  officeCity: string
  officeState: string
  officeCountry: string
  remoteWorker: boolean
  employeeStatus: string
  agencyStartDate: string
  supervisorCode: string
  businessUnitName: string
  industryAligned: string
  industryFocused: string
  leaveOfAbsenceStartDate: string | null
  leaveOfAbsenceEndDate: string | null
  employeeType: string
  careerStageLevel: string
  market: string
  employeeClass: string
  employeeStandardWeeklyHours: number
  parentJobFunction: string
  childJobFunction: string
  regularOrTemporary: string
}

export interface Address {
  addressStartDate: string
  addressEndDate: string
  homeState: string
}

export interface PersonalInformation {
  personalInformationStartDate: string
  personalInformationEndDate: string
  preferredFirstName: string
  preferredLastName: string
  firstName: string
  lastName: string
}

export interface UserPermission {
  permissionRole: string
  permissionRoleHFM: string
  permissionRoleAgency: string
  lionLogin: string
  employeeCode: string
  hfmEntityCode: string
  companyCode: string
  agencyCode: string
  effectiveFrom: string
  effectiveTo: string
}

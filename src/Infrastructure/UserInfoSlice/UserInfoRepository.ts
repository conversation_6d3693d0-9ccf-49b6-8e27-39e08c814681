/* eslint-disable @typescript-eslint/naming-convention */
//@DEV: we need to disable the naming convention rule here because the UserInfo API uses camelCase
import ILogger from '../../Domain/Common/ILogger'
import { UserInfo, EmployeeDetails, UserPermission } from '../../Domain/UserInfoSlice/UserInfo'
import { IUserInfoRepository } from '../../Application/UserInfoSlice/UserInfoRepository'
import { inject, injectable } from 'tsyringe'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { ApiWrapperPlatformRepository } from '../Common/Repositories/ApiWrapperPlatformRepository'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { config } from '../../Config/environment'

@injectable()
export default class UserInfoRepository extends ApiWrapperPlatformRepository implements IUserInfoRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getUserInfo(): Promise<UserInfo> {
    try {
      const { llid, token } = this.userSessionContext
      const employeeDetailsResponse = this.fetchFromPlatformApi<EmployeeDetails>(`/employees/${llid}`)
      const { employeeCode, firstName, lastName } = await employeeDetailsResponse
      const userName = `${firstName} ${lastName}`
      const userPermissions = this.fetchFromPlatformApi<UserPermission[]>(`/employee/${employeeCode}/business-roles`)
      const businessRole = (await userPermissions).map((obj: UserPermission) => obj.permissionRole)

      if (this.userSessionContext && llid && token) {
        this.logger.info(`User session context found for LLID: ${llid}, generating third-party API tokens.`)
        await this.generateThirdPartyApisToken()
      }

      return {
        employeeCode: employeeCode,
        businessRole: [...new Set(businessRole)],
        userName: userName
      }
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching UserInfo.')
    }
  }

  /**
   * Retrieves user information by Lion Login ID (LLID) for public access.
   *
   * This method is designed for PUBLIC endpoints that don't require user authentication.
   * Unlike the standard getUserInfo() method which relies on user session context,
   * this method uses alternative authentication approaches to call the platform API:
   *
   * 1. API Key authentication - Uses X-api-key header only
   * 2. Service Token authentication - Uses PLATFORM_SERVICE_TOKEN if available
   *
   * This approach is necessary because:
   * - The endpoint is marked as public (no user token available)
   * - Platform API still requires some form of authentication
   * - Standard on-behalf-of (OBO) token flow requires a user token
   * - Client credentials flow may not be configured for this use case
   *
   * The method fetches employee details and business roles from the platform API
   * and returns combined user information including employeeCode, businessRole array,
   * and userName (firstName + lastName).
   *
   * @param llid - Lion Login ID of the user to retrieve
   * @returns Promise<UserInfo> - User information including employee code, business roles, and name
   * @throws FetchRequestFailedError - When all authentication approaches fail or API calls fail
   */
  async getUserByLLID(llid: string): Promise<UserInfo> {
    this.logger.info(`Starting getUserByLLID for llid: ${llid}`)
    try {
      this.logger.info(`Fetching employee details for llid: ${llid}`)
      const employeeDetailsResponse = this.fetchFromPlatformApiPublic<EmployeeDetails>(`employees/${llid}`)
      const { employeeCode, firstName, lastName } = await employeeDetailsResponse
      const userName = `${firstName} ${lastName}`

      this.logger.info(`Fetching business roles for employeeCode: ${employeeCode}`)
      const userPermissions = this.fetchFromPlatformApiPublic<UserPermission[]>(
        `employee/${employeeCode}/business-roles`
      )
      const businessRole = (await userPermissions).map((obj: UserPermission) => obj.permissionRole)

      const result = {
        employeeCode: employeeCode,
        businessRole: [...new Set(businessRole)],
        userName: userName
      }

      this.logger.info(`Successfully retrieved user info for llid: ${llid}`)
      return result
    } catch (error) {
      this.logger.error(`Failed to fetch user by LLID ${llid}: ${error}`)
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching user by LLID.', { llid })
    }
  }

  private async fetchFromPlatformApiPublic<TResponse>(endpoint: string): Promise<TResponse> {
    this.logger.info(`Making public API call to: ${endpoint}`)

    const headers: Record<string, string> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'X-api-key': config.platformApiKey || 'f0e0540f-123a-4b4c-bf66-e7fbb4239ff4',
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'Content-Type': 'application/json'
    }

    this.logger.info(`Using API key: ${config.platformApiKey ? 'From config' : 'Default'}`)
    return await this.get(endpoint, headers)
  }

  async generateThirdPartyApisToken(): Promise<void> {
    const platformTokenTask = this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.platformApiScopes,
      `platform-token-${this.userSessionContext.llid}`
    )

    const projectTokenTask = this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.projectsApiScopes,
      `project-token-${this.userSessionContext.llid}`
    )

    const timesheetTokenTask = this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.timesheetsApiScopes,
      `timesheets-token-${this.userSessionContext.llid}`
    )

    try {
      await Promise.all([platformTokenTask, projectTokenTask, timesheetTokenTask])
    } catch (error) {
      this.logger.error(`Error generating third-party API tokens: ${error}`)
    }
  }
}

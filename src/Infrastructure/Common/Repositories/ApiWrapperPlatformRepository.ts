import { ApiWrapperRepository } from './ApiWrapperRepository'
import { config } from '../../../Config/environment'
import ILogger from '../../../Domain/Common/ILogger'
import { UserSessionContext } from '../../../Application/AuthSlice/UserSessionContext'
import { inject } from 'tsyringe'

export abstract class ApiWrapperPlatformRepository extends ApiWrapperRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger)
    this.baseURL = config.platformUrl
  }

  async fetchFromPlatformApi<TResponse>(endpoint: string): Promise<TResponse> {
    const token = await this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.platformApiScopes,
      `platform-token-${this.userSessionContext.llid}`
    )

    const headers: Record<string, string> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Authorization: `${token}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'X-api-key': 'f0e0540f-123a-4b4c-bf66-e7fbb4239ff4'
    }
    return await this.get(endpoint, headers)
  }

  async fetchAsPostFromPlatformApi<TRequest, TResponse>(endpoint: string, body: TRequest): Promise<TResponse> {
    const token = await this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.platformApiScopes,
      `platform-token-${this.userSessionContext.llid}`
    )

    const headers: Record<string, string> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Authorization: `${token}`,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'X-api-key': 'f0e0540f-123a-4b4c-bf66-e7fbb4239ff4'
    }
    return this.getAsPost<TRequest, TResponse>(endpoint, body, headers)
  }
}

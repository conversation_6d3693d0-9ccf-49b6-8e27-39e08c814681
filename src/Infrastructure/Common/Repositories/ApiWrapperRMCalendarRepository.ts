import { ApiWrapperRepository } from './ApiWrapperRepository'
import { config } from '../../../Config/environment'
import { UserSessionContext } from '../../../Application/AuthSlice/UserSessionContext'
import { inject } from 'tsyringe'
import ILogger from '../../../Domain/Common/ILogger'

export abstract class ApiWrapperRMCalendarRepository extends ApiWrapperRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger)
    this.baseURL = config.rmApiUrl
  }

  protected composeHeaders() {
    return {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Authorization: `${this.userSessionContext.token}`
    }
  }

  async fetchFromRMCalendarApi<TResponse>(endpoint: string): Promise<TResponse> {
    const headers = this.composeHeaders()
    return this.get<TResponse>(endpoint, headers)
  }

  async fetchAsPostFromRMCalendarApi<TRequest, TResponse>(endpoint: string, body: TRequest): Promise<TResponse> {
    const headers = this.composeHeaders()
    return this.getAsPost<TRequest, TResponse>(endpoint, body, headers)
  }

  async deleteFromRMCalendarApi<TResponse>(endpoint: string): Promise<boolean> {
    const headers = this.composeHeaders()
    const result = await this.customDelete<TResponse>(endpoint, headers)
    return typeof result === 'boolean' ? result : false
  }

  async putFromRMCalendarApi<TRequest, TResponse>(endpoint: string, body: TRequest): Promise<boolean> {
    const headers = this.composeHeaders()
    const result = await this.customPut<TRequest, TResponse>(endpoint, body, headers)
    return typeof result === 'boolean' ? result : false
  }
}

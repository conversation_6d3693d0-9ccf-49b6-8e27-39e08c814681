import { ApiWrapperRepository } from './ApiWrapperRepository'
import { config } from '../../../Config/environment'
import ILogger from '../../../Domain/Common/ILogger'
import { UserSessionContext } from '../../../Application/AuthSlice/UserSessionContext'
import { inject } from 'tsyringe'

export abstract class ApiWrapperProjectsRepository extends ApiWrapperRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger)
    this.baseURL = config.projectUrl
  }

  async fetchFromProjectApi<TResponse>(endpoint: string): Promise<TResponse> {
    const token = await this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.projectsApiScopes
    )
    const headers: Record<string, string> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Authorization: `${token}`
    }
    return await this.get(endpoint, headers)
  }

  async fetchAsPostFromProjectApi<TRequest, TResponse>(endpoint: string, body: TRequest): Promise<TResponse> {
    const token = await this.getTokenOnBehalfOf(
      this.userSessionContext.token.replace('Bearer ', ''),
      config.projectsApiScopes
    )
    const headers: Record<string, string> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      Authorization: `${token}`
    }
    return this.getAsPost<TRequest, TResponse>(endpoint, body, headers)
  }
}

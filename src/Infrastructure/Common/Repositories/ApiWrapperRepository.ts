import { RESTDataSource } from '@apollo/datasource-rest'
import ILogger from '../../../Domain/Common/ILogger'
import fetch from 'node-fetch'
import * as msal from '@azure/msal-node'
import { type OnBehalfOfRequest } from '@azure/msal-node'
import { config } from '../../../Config/environment'
import { container } from 'tsyringe'
import { ICacheService } from '../../../Application/Common/ICacheService'

export abstract class ApiWrapperRepository extends RESTDataSource {
  protected cacheService: ICacheService

  constructor(logger: ILogger) {
    super()
    this.logger = logger
    this.cacheService = container.resolve<ICacheService>('ICacheService')
  }

  // Base method to handle GET requests
  async get<TResponse>(endpoint: string, headers?: Record<string, string>): Promise<TResponse> {
    try {
      const response = (
        await fetch(`${this.baseURL}${endpoint}`, {
          method: 'GET',
          headers: headers
        })
      ).json() as TResponse

      return response
    } catch (error) {
      this.logger.error(`Error while fetching from ${endpoint}, Error: ${error}`)
      throw error
    }
  }

  // Base method to handle POST requests
  async post<TRequest>(url: string, body: TRequest, headers?: Record<string, string>) {
    try {
      // Convert the body to a JSON string
      const requestBody = JSON.stringify(body)

      const response = await super.post(`${this.baseURL}${url}`, {
        body: requestBody,
        headers: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'Content-Type': 'application/json',
          ...headers
        }
      })

      //handle BadRequest errors:
      if (!response) {
        const errorData = await response.json()
        if (response.status === 400) {
          const errorMessage = (errorData as { message?: string }).message || 'Unknown error occurred'
          const validationErrors = (errorData as { errors?: Record<string, string[]> }).errors || {}

          throw new Error(`HTTP 400 Error: ${errorMessage}. Validation issues: ${JSON.stringify(validationErrors)}`)
        } else {
          throw new Error(`HTTP error! status: ${response.status}`) // Handle HTTP errors
        }
      }
      return response
    } catch (error) {
      this.logger.error(`Error while sending POST request to ${url}, Error: ${error} `)
      throw error
    }
  }

  // Base method to handle POST requests
  async getAsPost<TRequest, TResponse>(
    url: string,
    body: TRequest,
    headers?: Record<string, string>
  ): Promise<TResponse> {
    try {
      const response = await this.post(url, body, headers)
      let result: TResponse

      if (typeof response.json === 'function') {
        result = (await response.json()) as TResponse
      } else {
        result = response as TResponse
      }

      return result
    } catch (error) {
      this.logger.error(`Error while casting POST response for ${url}, Error: ${error}`)
      throw error
    }
  }

  // OBO token exchange
  async getTokenOnBehalfOf(token: string, scopes: string, cacheKey: string): Promise<string> {
    const cachedToken = await this.cacheService.getCacheValue<string>(cacheKey)
    if (cachedToken) {
      this.logger.info(`Using cached token for key: ${cacheKey}`)
      return cachedToken
    }

    const onBehalfOfRequest: OnBehalfOfRequest = {
      oboAssertion: token,
      scopes: scopes.split(',')
    }

    const msalConfig: msal.Configuration = {
      auth: {
        clientId: config.azureAdClientId,
        clientSecret: config.azureAdClientSecret,
        authority: `https://login.microsoftonline.com/${config.azureAdTenantId}`
      }
    }

    try {
      this.logger.info(`Acquiring token on behalf of user ${cacheKey}`)
      const cca = new msal.ConfidentialClientApplication(msalConfig)
      const res = await cca.acquireTokenOnBehalfOf(onBehalfOfRequest)
      if (res) {
        await this.cacheService.setCacheValue(
          cacheKey,
          `Bearer ${res?.accessToken}`,
          parseInt(config.thirdPartyApiCacheExpirationTime)
        )
      }

      return res != null ? `Bearer ${res?.accessToken}` : ''
    } catch (error) {
      console.log(error)
    }
    return token
  }

  // Other HTTP methods can be similarly implemented if needed (e.g., PUT, DELETE)
  async customDelete<TResponse>(endpoint: string, headers?: Record<string, string>): Promise<TResponse | boolean> {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'DELETE',
        headers: headers
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Check if the response status is 204 No Content
      if (response.status === 204) {
        return true // Indicate success
      }

      const data = (await response.json()) as TResponse
      return data
    } catch (error) {
      this.logger.error(`Error while deleting from ${endpoint}, Error: ${error}`)
      throw error
    }
  }

  async customPut<TRequest, TResponse>(
    endpoint: string,
    body: TRequest,
    headers?: Record<string, string>
  ): Promise<TResponse | boolean> {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'PUT',
        headers: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(body)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Check if the response status is 204 No Content
      if (response.status === 204) {
        return true // Indicate success
      }

      const data = (await response.json()) as TResponse
      return data
    } catch (error) {
      this.logger.error(`Error while putting to ${endpoint}, Error: ${error}`)
      throw error
    }
  }
}

import { injectable } from 'tsyringe'
import { ICacheService } from '../../Application/Common/ICacheService'
import { KeyValueCache, InMemoryLRUCache } from '@apollo/utils.keyvaluecache'
import { config } from '../../Config/environment'

@injectable()
export default class CacheService implements ICacheService {
  private cache: KeyValueCache<string>

  constructor() {
    this.cache = new InMemoryLRUCache()
  }

  async getCacheValue<T>(key: string): Promise<T | undefined> {
    const value = await this.cache.get(key)
    return value ? (JSON.parse(value) as T) : undefined
  }

  async setCacheValue<T>(key: string, value: T, expirationTime: number = 0): Promise<void> {
    const options = { ttl: expirationTime > 0 ? expirationTime : +config.cacheExpirationTime }
    await this.cache.set(key, JSON.stringify(value), options)
  }

  async deleteCacheValue(key: string): Promise<void> {
    await this.cache.delete(key)
  }

  async clearCache(): Promise<void> {
    this.cache = new InMemoryLRUCache({
      maxSize: 100,
      ttl: 1000 * 60 * 5
    })
  }

  async addValueToKeyVault(workCode: string, key: string): Promise<void> {
    const expirationTime = 3600 // one hour optional
    const keys = (await this.getCacheValue<string[]>(workCode)) ?? []
    if (keys.length === 0) {
      await this.setCacheValue(workCode, [key], expirationTime)
      return
    }

    if (keys.includes(key)) {
      return
    }

    keys.push(key)
    await this.setCacheValue(workCode, keys, expirationTime)
  }

  async removeCachedValuesFromKeyVault(workCode: string): Promise<void> {
    const keys = await this.getCacheValue<string[]>(workCode)
    if (!keys) {
      return
    }

    const promises = [] as Promise<void>[]
    for (const key of keys) {
      promises.push(this.deleteCacheValue(key))
    }

    // Delete all cached values associated to the work code
    await Promise.all(promises)
    // Delete key vault associated to the work code
    await this.deleteCacheValue(workCode)
  }
}

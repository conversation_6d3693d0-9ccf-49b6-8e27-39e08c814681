import { inject, injectable } from 'tsyringe'

import { IBrandRepository } from '../../Application/BrandSlice/BrandRepository'
import { ApiWrapperPlatformRepository } from '../Common/Repositories/ApiWrapperPlatformRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { Brand } from '../../Domain/BrandSlice/Brand'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { config } from '../../Config/environment'
import { CacheProps } from '../../Domain/Common/CacheProps'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

@injectable()
export default class BrandRepository extends ApiWrapperPlatformRepository implements IBrandRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  @cache(
    (params: string[]) =>
      ({
        module: 'BRAND',
        key: `${params.join(',')}`,
        cacheDuration: parseInt(config.cacheExpirationTime)
      }) as CacheProps
  )
  async getBrands(params: string[]): Promise<Brand[]> {
    const endpoint = `brands/list/${params.join(',')}`
    try {
      return this.fetchFromPlatformApi<Brand[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching brands.', { endpoint })
    }
  }
}

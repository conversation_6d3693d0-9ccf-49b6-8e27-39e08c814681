import { inject, injectable } from 'tsyringe'
import { ITasksRepository } from '../../Application/TasksSlice/ITasksRepository'
import { ApiWrapperProjectsRepository } from '../Common/Repositories/ApiWrapperProjectsRepository'
import { config } from '../../Config/environment'
import ILogger from '../../Domain/Common/ILogger'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { CacheProps } from '../../Domain/Common/CacheProps'
import { Task } from '../../Domain/TasksSlice/Task'
import { PagingProject } from '../../Domain/Common/PagingProject'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

const projectsCacheExpirationTime: number = parseInt(config.projectsCacheExpirationTime)

@injectable()
export default class TasksRepository extends ApiWrapperProjectsRepository implements ITasksRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  @cache(
    (projectId: string) =>
      ({ module: 'TasksByProject', key: projectId, cacheDuration: projectsCacheExpirationTime }) as CacheProps
  )
  async getTasksByProjectId(projectId: string): Promise<Task[]> {
    this.logger.info(`Cache skipped for projectId: ${projectId}. Fetching projects from API`)
    const endpoint = `tasks/byproject/${projectId}?pageSize=1000&pageNumber=1`
    try {
      const tasks = await this.fetchFromProjectApi<PagingProject<Task>>(endpoint)
      return tasks.result
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching by project id.', { endpoint })
    }
  }

  @cache(
    (taskId: string) => ({ module: 'Task', key: taskId, cacheDuration: projectsCacheExpirationTime }) as CacheProps
  )
  async getTaskByProjectIdAndTaskId(projectId: string, taskId: string): Promise<Task[]> {
    this.logger.info(`Cache skipped for taskId: ${taskId}. Fetching task from API`)
    const endpoint = `tasks/byproject/${projectId}?taskId=${taskId}&pageSize=20&pageNumber=1`
    try {
      const tasks = await this.fetchFromProjectApi<PagingProject<Task>>(endpoint)
      if (!tasks || !tasks.result) {
        throw new Error('Invalid task by project id data.')
      }

      return tasks.result
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching task by project and task id.', { endpoint })
    }
  }
}

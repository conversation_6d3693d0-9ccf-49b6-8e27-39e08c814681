import { inject, injectable } from 'tsyringe'
import { UsersGroup } from '../../Domain/UsersGroupsSlice/UsersGroup'
import { UsersGroupRequest } from '../../Application/UsersGroupsSlice/UsersGroupRequest'
import { IUsersGroupRepository } from '../../Application/UsersGroupsSlice/IUsersGroupRepository'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { CacheProps } from '../../Domain/Common/CacheProps'
import { config } from '../../Config/environment'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

const usersGroupsCacheExpirationTime: number = parseInt(config.usersGroupsCacheExpirationTime)

@injectable()
export default class UsersGroupRepository extends ApiWrapperRMCalendarRepository implements IUsersGroupRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  @cache(
    (resourceManagerId: string) =>
      ({ module: 'UserGroups', key: resourceManagerId, cacheDuration: usersGroupsCacheExpirationTime }) as CacheProps
  )
  async getUsersGroups(params: UsersGroupRequest): Promise<UsersGroup[]> {
    const { resourceManagerId } = params
    this.logger.info(`Cache skipped for resourceManagerId: ${resourceManagerId}. Fetching user groups from API`)
    const endpoint = `usergroups?resourceManagerId=${resourceManagerId}`
    try {
      return await this.fetchFromRMCalendarApi<UsersGroup[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching user groups.', { endpoint })
    }
  }

  async getUserGroupById(userGroupId: number): Promise<UsersGroup> {
    throw new Error(`getUserGroupById Not implemented for userGroupId ${userGroupId}`)
  }
}

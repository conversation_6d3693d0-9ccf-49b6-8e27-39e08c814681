import { inject, injectable } from 'tsyringe'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { ResourceAndPlaceholder } from '../../Domain/ResourceAndPlaceholderSlice/ResourceAndPlaceholder'
import { IResourceAndPlaceholderRepository } from '../../Application/ResourceAndPlaceholderSlice/IResourceAndPlaceholderRepository'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

@injectable()
export default class ResourceAndPlaceholderRepository
  extends ApiWrapperRMCalendarRepository
  implements IResourceAndPlaceholderRepository
{
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getResourcesAndPlaceholdersByProjectManager(
    userId: string,
    projectIds: string[]
  ): Promise<ResourceAndPlaceholder[]> {
    const body = {
      projectIds: projectIds
    }
    const endpoint = `project-managers/${userId}/resourcesAndplaceholders`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, ResourceAndPlaceholder[]>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(
        this.logger,
        error,
        'Error fetching resources and placeholders by project manager.',
        {
          endpoint
        }
      )
    }
  }

  async getResourcesAndPlaceholdersByResourceManager(
    resourceManagerId: string,
    agencyCode: string[],
    costCenterCode: string[],
    location: string[]
  ): Promise<ResourceAndPlaceholder[]> {
    const body = {
      agencyCode: agencyCode,
      costCenterCode: costCenterCode,
      location: location
    }
    const endpoint = `resource-managers/${resourceManagerId}/resourcesAndplaceholders`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, ResourceAndPlaceholder[]>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(
        this.logger,
        error,
        'Error fetching resources and placeholders by resource manager.',
        {
          endpoint
        }
      )
    }
  }

  async searchResourcesAndPlaceholders(searchName: string): Promise<ResourceAndPlaceholder[]> {
    const endpoint = `users/searchByKeyword?searchKeyword=${searchName}`
    try {
      return await this.fetchFromRMCalendarApi<ResourceAndPlaceholder[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching resources and placeholders.', { endpoint })
    }
  }
}

/* eslint-disable @typescript-eslint/no-unused-vars */
import { inject, injectable } from 'tsyringe'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { AuditDto } from '../../Application/AuditTrailSlice/AuditDto'
import { IAuditTrailRepository } from '../../Application/AuditTrailSlice/IAuditTrailRepository'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'

@injectable()
export default class AuditTrailRepository extends ApiWrapperRMCalendarRepository implements IAuditTrailRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getAuditTrailForLoggedInUser(userLoggedInExternalId: string): Promise<AuditDto[]> {
    const endpoint = `audit-trail/${userLoggedInExternalId}`
    try {
      return await this.fetchFromRMCalendarApi<AuditDto[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching AuditLog', {
        endpoint
      })
    }
  }

  async clearAuditTrailForLoggedInUser(userLoggedInExternalId: string): Promise<boolean> {
    const endpoint = `audit-trail/${userLoggedInExternalId}`
    try {
      return await this.deleteFromRMCalendarApi<boolean>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching delete assignment.', { endpoint })
    }
  }
}

import { IProjectRepository } from '../../Application/ProjectSlice/IProjectRepository'
import { Project } from '../../Domain/ProjectSlice/Project'
import { config } from '../../Config/environment'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { CacheProps } from '../../Domain/Common/CacheProps'
import { ApiWrapperProjectsRepository } from '../Common/Repositories/ApiWrapperProjectsRepository'
import { inject, injectable } from 'tsyringe'
import ILogger from '../../Domain/Common/ILogger'
import { PagingProject } from '../../Domain/Common/PagingProject'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { handleHttpStatus, HttpStatusError } from '../../GraphQL/Common/CustomErrors/HandleHttpStatus'

const projectsCacheExpirationTime: number = parseInt(config.projectsCacheExpirationTime)

@injectable()
export default class ProjectRepository extends ApiWrapperProjectsRepository implements IProjectRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  @cache(
    (workCode: string) =>
      ({ module: 'Projects', key: workCode, cacheDuration: projectsCacheExpirationTime }) as CacheProps
  )
  async getProjectsByProjectManagerWorkCode(workCode: string): Promise<Project[] | null> {
    this.logger.info(`Cache skipped for workCode: ${workCode}. Fetching projects from API`)
    const roles = 'ProjectManager,AdditionalManager'
    const endpoint = `projects/userroles?UserId=${workCode}&Roles=${roles}&pageSize=1000&pageNumber=1`
    try {
      const projects = await handleHttpStatus(this.fetchFromProjectApi<PagingProject<Project>>(endpoint))

      let projectsList = projects.result
      if (!projectsList || projectsList.length === 0) {
        this.logger.info(`No projects found for workCode: ${workCode}`)
        return null
      }
      this.logger.info(`Projects found for workCode: ${workCode}`)
      projectsList = projectsList.filter((project) => project.ppmInUse.toLowerCase() === 'workfront')
      return projectsList
    } catch (error) {
      // Ensure error is wrapped with extensions.status for FetchRequestFailedError
      if (error.status === 404) {
        this.logger.warn(`No projects found for workCode: ${workCode}.`)
        return null
      } else {
        throw new FetchRequestFailedError(
          this.logger,
          error instanceof HttpStatusError ? { ...error, extensions: { status: error.status } } : error,
          'Error fetching projects for project manager.',
          { endpoint }
        )
      }
    }
  }

  async getProjectsByProjectId(projectId: string): Promise<Project> {
    this.logger.info(`Cache skipped for projectId: ${projectId}. Fetching projects from API`)
    const endpoint = `projects/${projectId}`
    try {
      return await this.fetchFromProjectApi<Project>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching project.', { endpoint })
    }
  }
}

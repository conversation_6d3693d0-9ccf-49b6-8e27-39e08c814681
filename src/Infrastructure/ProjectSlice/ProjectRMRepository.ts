import { config } from '../../Config/environment'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { CacheProps } from '../../Domain/Common/CacheProps'
import { inject, injectable } from 'tsyringe'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import { IProjectRMRepository } from '../../Application/ProjectSlice/IProjectRMRepository'
import { ProjectRM } from '../../Domain/ProjectSlice/ProjectRM'
import { TaskWithProjectId } from '../../Domain/TasksSlice/Task'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

const projectsCacheExpirationTime: number = parseInt(config.projectsCacheExpirationTime)

@injectable()
export default class ProjectRMRepository extends ApiWrapperRMCalendarRepository implements IProjectRMRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  @cache(
    (workCode: string) =>
      ({ module: 'ProjectsRM', key: workCode, cacheDuration: projectsCacheExpirationTime }) as CacheProps
  )
  async getProjectsByResourceManagerId(workCode: string, isExpandedPlaceholderApplied: boolean): Promise<ProjectRM[]> {
    this.logger.info(`Cache skipped for workCode: ${workCode}. Fetching projects for resource manager from RM API`)
    const endpoint = `projects-resource-managers/?resourceManagerId=${workCode}&isExpandedPlaceholderApplied=${isExpandedPlaceholderApplied}`

    try {
      return await this.fetchFromRMCalendarApi<ProjectRM[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching projects for resource manager.', {
        endpoint
      })
    }
  }

  async getTasksByProjectList(projectIds: string[]): Promise<TaskWithProjectId[]> {
    this.logger.info(`Getting tasks for projects with ids: ${projectIds}`)
    const endpoint = `tasks-project`

    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, TaskWithProjectId[]>(endpoint, {
        projectIds: projectIds
      })
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching tasks with provided project ids.', {
        endpoint
      })
    }
  }
}

import { inject, injectable } from 'tsyringe'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { ITimesheetsRepository } from '../../Application/TimesheetsSlice/TimesheetsRepository'
import { TimeOffRequest, TimesheetsRequest } from '../../Application/TimesheetsSlice/TimesheetsRequests'
import { ApiWrapperTimesheetsRepository } from '../Common/Repositories/ApiWrapperTimesheetsRepository'
import { TimeOffDetails, Timesheets } from '../../Domain/TimesheetsSlice/Timesheets'

@injectable()
export default class TimesheetsRepository extends ApiWrapperTimesheetsRepository implements ITimesheetsRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }
  getTimesheets(): Promise<Timesheets[]> {
    throw new Error('Method not implemented.')
  }

  async getTimesheetsForResource(params: TimesheetsRequest): Promise<Timesheets[]> {
    try {
      const response = await this.fetchAsPostFromTimesheetsApi<TimesheetsRequest, Timesheets[]>(
        'Timesheet/gettimesheetinformation',
        params
      )
      return response
    } catch (error) {
      this.logger.error('Error fetching Timesheets information')
      throw error
    }
  }

  async getTimeOffForResource(params: TimeOffRequest): Promise<TimeOffDetails[]> {
    const serviceParams = { ...params, status: 5 }
    try {
      const response = await this.fetchAsPostFromTimesheetsApi<TimeOffRequest, TimeOffDetails[]>(
        'Timesheet/getusertimeoffdetails',
        serviceParams
      )
      return response
    } catch (error) {
      this.logger.error('Error fetching TimeOff information')
      throw error
    }
  }
}

/* eslint-disable @typescript-eslint/naming-convention */
//@DEV: we need to disable the naming convention rule here because the Workfront API uses camelCase
import { ApiWrapperRepository } from '../Common/Repositories/ApiWrapperRepository'
import ILogger from '../../Domain/Common/ILogger'
import { Workfront } from '../../Domain/WorkfrontSlice/Workfront'
import { IWorkfrontRepository } from '../../Application/WorkfrontSlice/WorkfrontRepository'
import { inject, injectable } from 'tsyringe'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

interface WorkfrontApiResponse {
  ID: string
  name: string
  objCode: string
  'DE:csLionLoginID'?: string | null
}

@injectable()
export default class WorkfrontRepository extends ApiWrapperRepository implements IWorkfrontRepository {
  constructor(@inject('ILogger') logger: ILogger) {
    super(logger)
  }

  async getWorkfrontUser({
    hostname,
    imsToken,
    userID
  }: {
    hostname: string
    imsToken: string
    userID: string
  }): Promise<Workfront> {
    try {
      if (!imsToken || !userID || !hostname) {
        throw new Error('Missing required data for API call')
      }

      this.baseURL = `https://${hostname}`

      const endpoint = `/attask/api-internal/USER/${userID}?fields=DE:csLionLoginID&apiKey=69me9im5a0pfybmd`

      const response = await this.get<{ data: WorkfrontApiResponse } & WorkfrontApiResponse>(endpoint, {
        authorization: `Bearer ${imsToken}`,
        'content-type': 'application/json'
      })

      const wfData = response.data

      return {
        id: wfData?.ID,
        name: wfData.name,
        objCode: wfData.objCode,
        lionLoginID: wfData['DE:csLionLoginID'] || null
      }
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching WorkfrontUser.')
    }
  }
}

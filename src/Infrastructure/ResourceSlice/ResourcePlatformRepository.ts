import { inject, injectable } from 'tsyringe'
import { cache } from '../../Application/Common/CustomDecorators/Cache'
import { config } from '../../Config/environment'
import { CacheProps } from '../../Domain/Common/CacheProps'
import { ResourcePlatform } from '../../Domain/ResourceSlice/ResourcePlatform'
import { IResourcePlatformRepository } from '../../Application/ResourceSlice/IResourcePlatformRepository'
import { ApiWrapperPlatformRepository } from '../Common/Repositories/ApiWrapperPlatformRepository'
import ILogger from '../../Domain/Common/ILogger'
import { AgencyRule } from '../../Domain/ResourceSlice/AgencyRule'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { Employee } from '../../Domain/ResourceAndPlaceholderSlice/Employee'
import { PlatformOrgStructure } from '../../Domain/ResourceSlice/PlatformOrgStructure'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { Agency } from '../../Domain/ResourceSlice/Agency'
import pLimit from 'p-limit'
import { WorkFrontData } from '../../Domain/ResourceSlice/WorkFrontData'
import { Holiday } from '../../Domain/ResourceSlice/Holiday'
import { PlaceholderByWorkCode } from '../../Domain/ResourceSlice/PlaceholderByWorkCode'

const resourcesCacheExpirationTime: number = parseInt(config.resourcesCacheExpirationTime)

@injectable()
export default class ResourcePlatformRepository
  extends ApiWrapperPlatformRepository
  implements IResourcePlatformRepository
{
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private RESOURCE_CHUNK_SIZE = 25
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private CONCURRENCY_LIMIT = 5 // Set your desired concurrency limit

  @cache(
    (resourceId: string) =>
      ({ module: 'Resource', key: resourceId, cacheDuration: resourcesCacheExpirationTime }) as CacheProps
  )
  async getResourceById(resourceId: string): Promise<ResourcePlatform> {
    const endpoint = `employees/${resourceId}`
    try {
      return await this.fetchFromPlatformApi<ResourcePlatform>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }

  async getResourcesByWorkCodes(workCodes: string[]): Promise<ResourcePlatform[]> {
    const numberChunks = Math.ceil(workCodes.length / this.RESOURCE_CHUNK_SIZE)
    const limit = pLimit(this.CONCURRENCY_LIMIT)
    const chunkPromises = []

    for (let i = 0; i < numberChunks; i++) {
      const workCodeChunk = workCodes.slice(i * this.RESOURCE_CHUNK_SIZE, (i + 1) * this.RESOURCE_CHUNK_SIZE)
      const endpoint = `employees?employeeCodes=${encodeURIComponent(workCodeChunk.join(','))}`
      chunkPromises.push(limit(() => this.fetchFromPlatformApi<ResourcePlatform[]>(endpoint)))
    }

    const endpoint = 'employees?employeeCodes'
    try {
      const resourcesInfo = await Promise.all(chunkPromises)
      return resourcesInfo.flat()
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching resources.', { endpoint })
    }
  }

  async getUsersBySearchName(searchName: string): Promise<Employee[]> {
    const shortenedSearchName = searchName.trim().slice(0, 10)
    const endpoint = `employees/search?q=${shortenedSearchName}&EmployeeStatus=active`
    try {
      return this.fetchFromPlatformApi<Employee[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }

  @cache(
    (agencyCode: string) =>
      ({ module: 'Resources', key: agencyCode, cacheDuration: resourcesCacheExpirationTime }) as CacheProps
  )
  async getResourceAgencyRules(agencyCode: string) {
    const endpoint = `agency/${agencyCode}/agency-rules`
    try {
      return this.fetchFromPlatformApi<AgencyRule>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }

  @cache(
    (agencyCode: string) =>
      ({ module: 'Agencies', key: agencyCode, cacheDuration: resourcesCacheExpirationTime }) as CacheProps
  )
  async getResourceAgenciesRules(agencyCodes: string[]) {
    const endpoint = `agencies/agency-rules`
    try {
      return await this.fetchAsPostFromPlatformApi<unknown, AgencyRule[]>(endpoint, agencyCodes)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }

  @cache(
    (agencyCode: string) =>
      ({ module: 'Agency', key: agencyCode, cacheDuration: resourcesCacheExpirationTime }) as CacheProps
  )
  async getAgenciesData(agencyCodes: string[]) {
    const endpoint = `orgstructures?AgencyCode=${agencyCodes.join(',')}`
    try {
      return this.fetchFromPlatformApi<{ agency: { agencyCode: string; agencyName: string } }[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }

  @cache(
    (agencyCode: string) =>
      ({ module: 'UserOrgStructure', key: agencyCode, cacheDuration: resourcesCacheExpirationTime }) as CacheProps
  )
  async getUserOrgStructure(agencyCode: string, costCenterCode: string): Promise<PlatformOrgStructure[]> {
    let endpoint = `orgstructures?AgencyCode=${agencyCode}`
    if (costCenterCode) {
      endpoint += `&CostCenterCode=${costCenterCode}`
    }
    try {
      return this.fetchFromPlatformApi<PlatformOrgStructure[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching ors structure for resource.', { endpoint })
    }
  }

  async searchAgencyOrgStructure(searchTerm: string): Promise<Agency[]> {
    const endpoint = `agency/search?q=${searchTerm}`
    try {
      return this.fetchFromPlatformApi<Agency[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching ors structure for resource.', { endpoint })
    }
  }

  async getUserOrgStructureByAgencyCode(agencyCode: string): Promise<PlatformOrgStructure[]> {
    const endpoint = `orgstructures?AgencyCode=${agencyCode}`
    try {
      return this.fetchFromPlatformApi<PlatformOrgStructure[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching ors structure for resource.', {
        agencyCode
      })
    }
  }
  async getUserOrgStructureByCostCenterCode(costCenterCode: string): Promise<PlatformOrgStructure[]> {
    const endpoint = `orgstructures?CostCenter=${costCenterCode}`
    try {
      return this.fetchFromPlatformApi<PlatformOrgStructure[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching ors structure for resource.', {
        costCenterCode
      })
    }
  }
  async getWorkFrontData(id: string, entityName: string, appName: string): Promise<WorkFrontData[]> {
    const endpoint = `workfront/list/${id}?entityName=${entityName}&appName=${appName}`
    try {
      return this.fetchFromPlatformApi<WorkFrontData[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, `Error fetching workfront id for ${entityName}.`, {
        endpoint
      })
    }
  }

  async getPublicHolidaysCalendar(holidayCalendarCode: string, year: string): Promise<Holiday[]> {
    const endpoint = `publicholidaycalendar/${holidayCalendarCode}/${year}`
    try {
      const response = await this.fetchFromPlatformApi<[{ holidayAssignment: Holiday[] }]>(endpoint)

      return response[0].holidayAssignment
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching public holidays.', { endpoint })
    }
  }

  async getPlaceholderByWorkCode(workCode: string): Promise<PlaceholderByWorkCode[]> {
    const endpoint = `workcodes/search?q=${workCode}`
    try {
      return this.fetchFromPlatformApi<PlaceholderByWorkCode[]>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching agencies.', { endpoint })
    }
  }
}

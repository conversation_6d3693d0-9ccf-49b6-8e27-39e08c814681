import { IResourceRepository } from '../../Application/ResourceSlice/IResourceRepository'
import { Resource } from '../../Domain/ResourceSlice/Resource'
import { inject, injectable } from 'tsyringe'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { Sort } from '../../Domain/Common/Sort'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { OrgStructureByResourceAssignmentResponse } from '../../Domain/ResourceSlice/OrgStructureByResourceAssignmentResponse'

@injectable()
export default class ResourceRepository extends ApiWrapperRMCalendarRepository implements IResourceRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getResourceById(resourceId: string): Promise<Resource> {
    return {
      externalId: resourceId,
      name: 'Default Resource',
      isPlaceholder: true,
      integrationId: '',
      workCode: '',
      jobTitle: '',
      requiresAssignApproval: false
    }
  }

  async getResourcesForProjectManager(
    workCode: string,
    projectIds: string[],
    pageNumber: number,
    pageSize: number,
    sort: Sort,
    searchName?: string
  ): Promise<PagingResult<Resource>> {
    const body = {
      projectIds: projectIds,
      pageSize,
      pageNumber,
      searchName,
      sort: {
        field: sort.field,
        order: sort.order
      }
    }
    const endpoint = `project-managers/${workCode}/users`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, PagingResult<Resource>>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching resources for project manager.', {
        endpoint
      })
    }
  }

  async getResourcesForResourceManager(
    resourceManagerId: string,
    order: string,
    pageNumber: number,
    pageSize: number,
    searchName?: string
  ): Promise<PagingResult<Resource>> {
    const endpoint = `resource-managers/${resourceManagerId}/users?sort={"field":"name","order":"${order}"}&pageSize=${pageSize}&pageNumber=${pageNumber}&searchName=${searchName}`
    try {
      return await this.fetchFromRMCalendarApi<PagingResult<Resource>>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching resources for resource manager.', {
        endpoint
      })
    }
  }

  async getResourcesByFilters(
    userRole: string,
    resourceManagerId: string,
    projectIds: string[],
    brandIds: string[],
    userIds: string[],
    taskStates: string[],
    taskIds: string[],
    usersGroupIds: number[],
    startDate: string,
    endDate: string,
    isProjectFilterApplied: boolean,
    pageNumber: number,
    pageSize: number,
    sort: Sort
  ): Promise<PagingResult<Resource>> {
    const body = {
      userRole: userRole,
      resourceManagerId: resourceManagerId,
      projectIds: projectIds,
      brandIds: brandIds,
      userIds: userIds,
      taskStates: taskStates,
      taskIds: taskIds,
      usersGroupIds: usersGroupIds,
      startDate: startDate,
      endDate: endDate,
      isProjectFilterApplied: isProjectFilterApplied,
      pageSize,
      pageNumber,
      sort: {
        field: sort.field,
        order: sort.order
      }
    }
    const endpoint = `users/resourcesByFilters`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, PagingResult<Resource>>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching resources by filters.', {
        endpoint
      })
    }
  }

  async getOrgStructureByResourceAssignments(
    userRole: string,
    projectIds: string[],
    userId: string
  ): Promise<OrgStructureByResourceAssignmentResponse> {
    const body = {
      userRole: userRole,
      projectIds: projectIds,
      userId: userId
    }
    const endpoint = `users/orgStructureByResourceAssignments`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, OrgStructureByResourceAssignmentResponse>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(
        this.logger,
        error,
        'Error fetching Org Structure by Resource Assignments Values.',
        { endpoint }
      )
    }
  }
}

import { inject, injectable } from 'tsyringe'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import { IPlaceholderRepository } from '../../Application/PlaceholderSlice/IPlaceholderRepository'
import ILogger from '../../Domain/Common/ILogger'
import { PagingResult } from '../../Domain/Common/PagingResult'
import { Sort } from '../../Domain/Common/Sort'
import { CreateEditPlaceholderFilterValuesRequest } from '../../Domain/PlaceholderSlice/CreateEditPlaceholderFilterValuesRequest'
import { GetPlaceholderFilterValue } from '../../Domain/PlaceholderSlice/GetPlaceholderFilterValue'
import { Placeholder } from '../../Domain/PlaceholderSlice/Placeholder'
import { PlaceholderFilterValues } from '../../Domain/PlaceholderSlice/PlaceholderFilterValues'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import { OrgStructureByPlaceholderAssignmentResponse } from '../../Domain/PlaceholderSlice/OrgStructureByPlaceholderAssignmentResponse'

@injectable()
export default class PlaceholderRepository extends ApiWrapperRMCalendarRepository implements IPlaceholderRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getPlaceholdersForProjectManager(
    workCode: string,
    projectIds: string[],
    pageNumber: number,
    pageSize: number,
    sort: Sort
  ): Promise<PagingResult<Placeholder>> {
    const body = {
      projectIds: projectIds,
      pageSize,
      pageNumber,
      sort: {
        field: sort.field,
        order: sort.order
      }
    }
    const endpoint = `project-managers/${workCode}/placeholders`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, PagingResult<Placeholder>>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching placeholders for project Manager.', {
        endpoint
      })
    }
  }

  async getPlaceholdersForResourceManager(
    resourceManagerId: string,
    order: string,
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    pageNumber: number,
    pageSize: number,
    isExpandedPlaceholderApplied: boolean
  ): Promise<PagingResult<Placeholder>> {
    const body = {
      agencyCode: agencyCode,
      costCenterCode: costCenterCode,
      location: location,
      pageSize: pageSize,
      pageNumber: pageNumber,
      isExpandedPlaceholderApplied: isExpandedPlaceholderApplied,
      sort: {
        field: 'name',
        order: order
      }
    }
    const endpoint = `resource-managers/${resourceManagerId}/placeholders`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, PagingResult<Placeholder>>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching placeholders for resource Manager.', {
        endpoint
      })
    }
  }

  async getPlaceholdersByFilters(
    userRole: string,
    projectIds: string[],
    brandIds: string[],
    userIds: string[],
    taskStates: string[],
    taskIds: string[],
    usersGroupIds: number[],
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    startDate: string,
    endDate: string,
    isProjectFilterApplied: boolean,
    pageNumber: number,
    pageSize: number,
    sort: Sort,
    userId: string,
    isExpandedPlaceholderApplied: boolean
  ): Promise<PagingResult<Placeholder>> {
    const body = {
      userRole: userRole,
      projectIds: projectIds,
      brandIds: brandIds,
      userIds: userIds,
      taskStates: taskStates,
      taskIds: taskIds,
      usersGroupIds: usersGroupIds,
      agencyCode: agencyCode,
      costCenterCode: costCenterCode,
      location: location,
      startDate: startDate,
      endDate: endDate,
      isProjectFilterApplied: isProjectFilterApplied,
      pageSize,
      pageNumber,
      sort: {
        field: sort.field,
        order: sort.order
      },
      userId: userId,
      isExpandedPlaceholderApplied: isExpandedPlaceholderApplied
    }
    const endpoint = `users/placeholdersByFilters`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, PagingResult<Placeholder>>(endpoint, body)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching placeholders by filters.', {
        endpoint
      })
    }
  }

  async getPlaceholderFilterValuesByUserId(params: GetPlaceholderFilterValue): Promise<PlaceholderFilterValues> {
    const endpoint = `placeholders?userLoggedInExternalId=${params.userLoggedInExternalId}`
    try {
      return await this.fetchFromRMCalendarApi<PlaceholderFilterValues>(endpoint)
    } catch (error) {
      throw new Error(`Error fetching Placeholder Filter Values: ${params.userLoggedInExternalId}`)
    }
  }

  async createEditPlaceholderFilterValues(params: CreateEditPlaceholderFilterValuesRequest): Promise<boolean> {
    const endpoint = `placeholders/createupdate`
    try {
      return await this.fetchAsPostFromRMCalendarApi<CreateEditPlaceholderFilterValuesRequest, boolean>(
        endpoint,
        params
      )
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error saving Placeholder Filter Values.', { endpoint })
    }
  }

  async getOrgStructureByPlaceholderAssignments(
    userRole: string,
    projectIds: string[],
    agencyCode: string[],
    costCenterCode: string[],
    location: string[],
    userId: string
  ): Promise<OrgStructureByPlaceholderAssignmentResponse> {
    const body = {
      userRole: userRole,
      projectIds: projectIds,
      agencyCode: agencyCode,
      costCenterCode: costCenterCode,
      location: location,
      userId: userId
    }
    const endpoint = `users/orgStructureByPlaceholderAssignments`
    try {
      return await this.fetchAsPostFromRMCalendarApi<unknown, OrgStructureByPlaceholderAssignmentResponse>(
        endpoint,
        body
      )
    } catch (error) {
      throw new FetchRequestFailedError(
        this.logger,
        error,
        'Error fetching Org Structure by Placeholder Assignments Values.',
        { endpoint }
      )
    }
  }
}

/* eslint-disable @typescript-eslint/no-unused-vars */
import { inject, injectable } from 'tsyringe'
import { Assignment } from '../../Domain/TaskAssignmentSlice/Assignment'
import { AssignmentRequest } from '../../Application/TaskAssignmentSlice/AssignmentRequest'
import { IAssignmentRepository } from '../../Application/TaskAssignmentSlice/IAssignmentRepository'
import { ApiWrapperRMCalendarRepository } from '../Common/Repositories/ApiWrapperRMCalendarRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'
import { CreateAssignmentRequest } from '../../Application/TaskAssignmentSlice/CreateAssignmentRequest'
import { UpdateAssignmentRequest } from '../../Domain/TaskAssignmentSlice/UpdateAssignmentRequest'
import { ApproveRejectAssignment } from '../../Domain/TaskAssignmentSlice/ApproveRejectAssignment'

@injectable()
export default class AssignmentRepository extends ApiWrapperRMCalendarRepository implements IAssignmentRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getAssignments(params: AssignmentRequest): Promise<Assignment[]> {
    const endpoint = `taskassignmententries`
    try {
      return await this.fetchAsPostFromRMCalendarApi<AssignmentRequest, Assignment[]>(endpoint, params)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching assignments.', { endpoint })
    }
  }

  async updateAssignment(assignment: UpdateAssignmentRequest): Promise<boolean> {
    const endpoint = `taskassignmententries`
    try {
      return await this.putFromRMCalendarApi<UpdateAssignmentRequest, boolean>(endpoint, assignment)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching update assignment.', { endpoint })
    }
  }

  async getAssignmentById(assignmentId: string): Promise<Assignment> {
    const endpoint = `taskassignmententries/${assignmentId}`
    try {
      return await this.fetchFromRMCalendarApi<Assignment>(endpoint)
    } catch (error) {
      throw new Error(`Error fetching assignmentById: ${assignmentId}`)
    }
  }

  async createAssignment(params: CreateAssignmentRequest): Promise<Assignment> {
    const endpoint = `taskassignments`
    try {
      return await this.fetchAsPostFromRMCalendarApi<CreateAssignmentRequest, Assignment>(endpoint, params)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching create assignment.', { endpoint })
    }
  }

  async deleteAssignmentById(assignmentId: string, userLoggedInExternalId: string): Promise<boolean> {
    const endpoint = `taskassignmententries/${assignmentId}/${userLoggedInExternalId}`
    try {
      return await this.deleteFromRMCalendarApi<boolean>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching delete assignment.', { endpoint })
    }
  }

  async approveRejectAssignment(params: ApproveRejectAssignment): Promise<boolean> {
    const endpoint = `taskassignments/`
    try {
      return await this.putFromRMCalendarApi<ApproveRejectAssignment, boolean>(endpoint, params)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching approve or reject assignment.', {
        endpoint
      })
    }
  }
}

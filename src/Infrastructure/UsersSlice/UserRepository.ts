import { inject, injectable } from 'tsyringe'

import { IUserRepository } from '../../Application/UsersSlice/IUserRepository'
import { User } from '../../Domain/UsersSlice/User'
import { ApiWrapperPlatformRepository } from '../Common/Repositories/ApiWrapperPlatformRepository'
import ILogger from '../../Domain/Common/ILogger'
import { UserSessionContext } from '../../Application/AuthSlice/UserSessionContext'
import FetchRequestFailedError from '../../GraphQL/Common/CustomErrors/FetchRequestFailedError'

@injectable()
export default class UserRepository extends ApiWrapperPlatformRepository implements IUserRepository {
  constructor(
    @inject('ILogger') logger: ILogger,
    @inject(UserSessionContext) protected userSessionContext: UserSessionContext
  ) {
    super(logger, userSessionContext)
  }

  async getUserByLlid(llid: string): Promise<User> {
    const endpoint = `employees/${llid}`
    try {
      return this.fetchFromPlatformApi<User>(endpoint)
    } catch (error) {
      throw new FetchRequestFailedError(this.logger, error, 'Error fetching user lion id.', { endpoint })
    }
  }
}

PLATFORM_API_KEY=
PLATFORM_URL=https://app-roarv2-api-platform-dev.azurewebsites.net/
ALLOWED_ORIGINS=http://localhost:3000
RM_URL=https://app-roarv2-rm-api-dev.azurewebsites.net/api/
PROJECTS_URL=https://app-roarv2-projects-qa.azurewebsites.net/api/
CACHE_EXPIRATION_TIME=300
PROJECTS_CACHE_EXPIRATION_TIME=300
USERS_GROUPS_CACHE_EXPIRATION_TIME=300
RESOURCES_CACHE_EXPIRATION_TIME=300
APPINSIGHTS_INSTRUMENTATION_KEY=
# NODE_TLS_REJECT_UNAUTHORIZED='0' #Activate when you are working with the local API
name: RM BFF App

trigger:
  branches:
    include:
      - development
      - qa

parameters:
  - name: businessName
    displayName: 'Business name'
    type: string
    default: 'roarv2'
  - name: applicationName
    displayName: 'Application name'
    type: string
    default: 'RM'
  - name: deployToDev
    displayName: 'Deploy to Dev'
    type: boolean
    default: false
  - name: deployToQA
    displayName: 'Deploy to QA'
    type: boolean
    default: false
  - name: deployToUAT
    displayName: 'Deploy to UAT'
    type: boolean
    default: false
  - name: deployToProd
    displayName: 'Deploy to Prod'
    type: boolean
    default: false

variables:
  artifactName: 'drop'
  appName: 'Publicis.rOar.RM.Bff'
  ${{ if eq(variables['Build.SourceBranchName'], 'qa') }}:
    resolvedEnv: 'qa1'
  ${{ elseif eq(variables['Build.SourceBranchName'], 'development') }}:
    resolvedEnv: 'dev1'
  ${{ elseif eq(variables['Build.SourceBranchName'], 'uat') }}:
    resolvedEnv: 'uat1'
  ${{ elseif eq(variables['Build.SourceBranchName'], 'main') }}:
    resolvedEnv: 'prod1'
  ${{ else }}:
    resolvedEnv: ''
  subscriptionDev: 'svc-rOar-v2-DEV'
  subscriptionQa: 'svc-rOar-v2-DEV'
  subscriptionUat: 'SSC-rOar-v2-UAT-MG'
  subscriptionProd: 'SSC-rOar-v2-PRD-MG'
  zipFile: '$(Pipeline.Workspace)/${{ variables.artifactName}}/${{ variables.appName }}.zip'
  ${{ if or(eq(parameters.deployToDev, true), eq(variables.resolvedEnv, 'dev1')) }}:
    containerRegistryName: 'crroarv2rmdev1'
  ${{ elseif or(eq(parameters.deployToQA, true), eq(variables.resolvedEnv, 'qa1')) }}:
    containerRegistryName: 'crroarv2rmqa1'
  ${{ elseif or(eq(parameters.deployToUAT, true), eq(variables.resolvedEnv, 'uat1')) }}:
    containerRegistryName: 'crroarv2rmuat1'
  ${{ elseif or(eq(parameters.deployToProd, true), eq(variables.resolvedEnv, 'prod1')) }}:
    containerRegistryName: 'crroarv2rmprod1'

  containerRegistry: '${{ variables.containerRegistryName }}.azurecr.io'
  imageRepository: 'bff/roarv2/rm-bff'

stages:
  - stage: build
    jobs:
      - job: build
        displayName: 'Build'
        steps:
          - task: Npm@1
            displayName: 'npm install'
            inputs:
              command: 'install'
          - task: Npm@1
            displayName: 'npm run compile'
            inputs:
              command: 'custom'
              customCommand: 'run compile'
          - task: ArchiveFiles@2
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
              includeRootFolder: false
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
              replaceExistingArchive: true
              verbose: true
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact'
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      - job: build_docker
        displayName: 'Build Docker Image'
        steps:
          - task: Docker@2
            displayName: 'Build Docker Image'
            inputs:
              command: 'build'
              Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
              repository: '${{ variables.containerRegistry }}/${{ variables.imageRepository }}'
              buildContext: '$(Build.SourcesDirectory)'
              tags: |
                $(Build.BuildId)
                latest
          - task: Docker@2
            condition: succeeded()
            displayName: 'Push Docker Image'
            inputs:
              containerRegistry: '${{ variables.containerRegistryName }}'
              repository: '${{ variables.imageRepository }}'
              command: 'push'
              Dockerfile: '$(Build.SourcesDirectory)/Dockerfile'
              tags: |
                $(Build.BuildId)
                latest

  - ${{ if or(eq(parameters.deployToDev, true), eq(variables.resolvedEnv, 'dev1')) }}:
      - stage: deploy_dev
        displayName: 'Deploy to dev1'
        dependsOn: build
        condition: succeeded()
        pool:
          name: self
          vmImage: windows-2022
        jobs:
          - deployment: deploy
            displayName: 'Deploy BFF App to dev1'
            environment: 'rm-bff-dev1'
            variables:
              - group: 'rm-bff-dev1'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - template: 'app-deploy.yml'
                      parameters:
                        appName: 'bff-${{parameters.businessName}}-${{parameters.applicationName}}-dev1'
                        azureSubscription: ${{ variables.subscriptionDev }}
                        containerRegistry: ${{ variables.containerRegistry }}
                        imageRepository: ${{ variables.imageRepository }}
                        tag: $(Build.BuildId)
  - ${{ if or(eq(parameters.deployToQA, true), eq(variables.resolvedEnv, 'qa1')) }}:
      - stage: deploy_qa
        displayName: 'Deploy to qa1'
        dependsOn: build
        condition: succeeded()
        pool:
          name: selfqa1
          vmImage: windows-2022
        jobs:
          - deployment: deploy
            displayName: 'Deploy BFF App to qa1'
            environment: 'rm-bff-qa1'
            variables:
              - group: 'rm-bff-qa1'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - template: 'app-deploy.yml'
                      parameters:
                        appName: 'bff-${{parameters.businessName}}-${{parameters.applicationName}}-qa1'
                        azureSubscription: ${{ variables.subscriptionQa }}
                        containerRegistry: ${{ variables.containerRegistry }}
                        imageRepository: ${{ variables.imageRepository }}
                        tag: $(Build.BuildId)

  - ${{ if or(eq(parameters.deployToUAT, true), eq(variables.resolvedEnv, 'uat1')) }}:
      - stage: deploy_uat
        displayName: 'Deploy to uat1'
        dependsOn: build
        condition: succeeded()
        pool:
          name: selfuat1
          vmImage: windows-2022
        jobs:
          - deployment: deploy
            displayName: 'Deploy BFF App to uat1'
            environment: 'rm-bff-uat1'
            variables:
              - group: 'rm-bff-uat1'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - template: 'app-deploy.yml'
                      parameters:
                        appName: 'bff-${{parameters.businessName}}-${{parameters.applicationName}}-uat1'
                        azureSubscription: ${{ variables.subscriptionUat }}
                        containerRegistry: ${{ variables.containerRegistry }}
                        imageRepository: ${{ variables.imageRepository }}
                        tag: $(Build.BuildId)

  - ${{ if or(eq(parameters.deployToProd, true), eq(variables.resolvedEnv, 'prod1')) }}:
      - stage: deploy_prod
        displayName: 'Deploy to prod1'
        dependsOn: build
        condition: succeeded()
        pool:
          name: selfprod1
          vmImage: windows-2022
        jobs:
          - deployment: deploy
            displayName: 'Deploy BFF App to prod1'
            environment: 'rm-bff-prod1'
            variables:
              - group: 'rm-bff-prod1'
            strategy:
              runOnce:
                deploy:
                  steps:
                    - template: 'app-deploy.yml'
                      parameters:
                        appName: 'bff-${{parameters.businessName}}-${{parameters.applicationName}}-prod1'
                        azureSubscription: ${{ variables.subscriptionProd }}
                        containerRegistry: ${{ variables.containerRegistry }}
                        imageRepository: ${{ variables.imageRepository }}
                        tag: $(Build.BuildId)

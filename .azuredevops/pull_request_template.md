## Description

## Pull request classification

- [] Bugfix
- [] New Feature
- [] QA Release
- [] SBX Release
- [] PROD Release

## How Has This Been Tested?

Describe the tests you ran to verify your changes. Provide instructions for us to reproduce. Please also list details relevant to your test setup and attach evidence images.

- [] Unit tests
- [] Integration test
- [] Functional tests in local environment
- [] Tested targeting DEV from a local environment
- [] Tested targeting QA from a local environment
- [] Tested targeting SBX from a local environment
- [] Tested targeting PROD from a local environment

**Test Configuration**:

- OS Version: Windows 11
- Node Version: 18 LTS
- Lion UI ^1.5.5

# Check list for good practices

- [] I have done a self review of my own code
- [] My code follows the style guidelines of this project.
- [] I have commented my code, particularly in hard to understand areas.
- [] New and existing unit tests pass locally with my changes
- [] I have made the corresponding changes to the documentation.
- [] My changes do not generate new warnings
- [] I have added tests that show that my solution is effective or that my function does what it claims to do

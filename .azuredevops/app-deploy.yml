parameters:
  - name: appName
    type: string
  - name: azureSubscription
    type: string
  - name: containerRegistry
    type: string
  - name: imageRepository
    type: string
  - name: tag
    type: string
    default: latest

steps:
  - checkout: self
    submodules: true
  - task: AzureWebAppContainer@1
    displayName: 'Azure Web App on Container Deploy'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      appName: ${{ parameters.appName }}
      containers: ${{ parameters.containerRegistry}}/${{ parameters.imageRepository }}:${{ parameters.tag }}

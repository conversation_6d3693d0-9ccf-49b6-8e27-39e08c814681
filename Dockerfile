FROM node:22.3.0-alpine AS builder

RUN mkdir -p /app
WORKDIR /app

ENV RM_URL=https://app-roarv2-rm-api-dev.azurewebsites.net/api/
ENV PROJECTS_URL=https://app-roarv2-projects-qa.azurewebsites.net/api/
ENV PLATFORM_URL=https://app-roarv2-api-platform-dev.azurewebsites.net/
ENV CACHE_EXPIRATION_TIME=300
ENV PROJECTS_CACHE_EXPIRATION_TIME=300
ENV USERS_GROUPS_CACHE_EXPIRATION_TIME=300
ENV PLATFORM_API_KEY=
ENV APPLICATIONINSIGHTS_CONNECTION_STRING=
ENV APPINSIGHTS_INSTRUMENTATION_KEY=
ENV NODE_ENV=dev

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run compile

FROM node:22.3.0-alpine AS runner
WORKDIR /app
COPY --from=builder /app/package.json .
COPY --from=builder /app/package-lock.json .
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 4000
CMD [ "npm", "run", "prod" ]
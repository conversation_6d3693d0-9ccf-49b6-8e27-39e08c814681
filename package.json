{"name": "roar-bff-rm", "version": "1.0.0", "description": "RM BFF Layer", "main": "index.js", "scripts": {"compile": "tsc", "dev": "nodemon", "prod": "node ./dist/index.js", "start": "npm run compile && node ./dist/index.js", "startServer": "npm install && npm run compile && node ./dist/index.js", "lint": "eslint . --fix --max-warnings=0", "format": "prettier . --write", "prepare": "npx husky install", "pre-commit": "npm run format && npm run lint", "pre-push": "npm run compile"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@apollo/datasource-rest": "^6.3.0", "@apollo/server": "^4.10.4", "@azure/msal-node": "^2.15.0", "applicationinsights": "^3.3.0", "dayjs": "^1.11.11", "graphql": "^16.8.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "reflect-metadata": "^0.2.2", "tsyringe": "^4.8.0", "typescript": "^5.5.4", "winston": "^3.14.2", "winston-azure-application-insights": "^4.0.0"}, "devDependencies": {"@eslint/js": "^9.5.0", "@types/applicationinsights": "^0.20.0", "@types/node": "^20.14.9", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-json": "^4.0.0", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "globals": "^15.6.0", "husky": "^9.0.11", "nodemon": "^3.1.4", "prettier": "^3.3.2", "ts-node": "^10.9.2", "typescript-eslint": "^7.13.1"}}
{"env": {"browser": true, "es2021": true}, "extends": ["plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "react"], "overrides": [{"env": {"node": true}, "files": [".eslintrc.{js,cjs}"], "parserOptions": {"sourceType": "script"}}], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "rules": {"prefer-const": "error", "semi": [2, "never"], "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/method-signature-style": "off", "camelcase": "off", "@typescript-eslint/naming-convention": ["error", {"selector": "function", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "class", "format": ["PascalCase"]}, {"selector": "classProperty", "format": ["camelCase"]}, {"selector": "interface", "format": ["PascalCase"]}, {"selector": "parameter", "format": ["camelCase", "PascalCase"], "leadingUnderscore": "allow"}, {"selector": "property", "format": ["camelCase"]}, {"selector": "objectLiteralProperty", "format": ["UPPER_CASE", "camelCase"]}, {"selector": "method", "format": ["camelCase"]}]}}